# job-is-live-audience-builder

# How to run locally
1. Copy application-env-local.yml.examples to application-env-local.yml and set up configuration.

2. You need to start Envoy first.
```bash
envoy-qa-start.sh -d DocServiceDaemon-marvin,advertiserservice,one-graph,raven-delivery,communications-hub-api-gateway,feature-management-service-ro -n job-is-live-audience-builder
```

3. Start job:
```bash
MESH_HOST_AND_PORT=localhost:4444 ./gradlew run
```
example: MESH_HOST_AND_PORT=localhost:4444 ./gradlew run 

See the [wiki](https://wiki.indeed.com/display/SMBPlatform/Last+Touch+Before+Dormant+Email) for more information

# Spotless information
This project now uses spotless to format the code. 
You can run the following command to autoformat code: ./gradlew spotlessApply
The build will fail if the code is not formatted, and it will prompt you to run spotlessApply to fix the error.
