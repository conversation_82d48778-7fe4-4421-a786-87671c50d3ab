# WARNING: DO NOT EDIT THIS FILE BY HAND!
# Resolving a project will strictly use the versions listed
# here, so bumping a dependency by hand will likely result in
# missing transitive dependency updates. Rather than editing by hand:
# 
# To re-lockdown, run:
#   ./gradlew lockdown
# To bump up a dependency to the latest version, run:
#   ./gradlew lockdown --update indeed:common-util
# To downgrade or force a dependency to a specific version, run:
#   ./gradlew lockdown --update indeed:common-util:3.0.15
# You may use commas to force multiple libraries.
# 
# Always double-check the new lockdown file when bumping a single library.
# 
ai.djl.huggingface:tokenizers:0.33.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
ai.djl:api:0.33.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
args4j:args4j:2.0.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
axis:axis-jaxrpc:1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
axis:axis-saaj:1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
axis:axis-wsdl4j:1.5.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
axis:axis:1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:amazon-sqs-java-extended-client-lib:2.1.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:aws-java-sdk-core:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:aws-java-sdk-dynamodb:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:aws-java-sdk-kms:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:aws-java-sdk-s3:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:aws-java-sdk-sqs:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:aws-java-sdk-sts:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.amazonaws:jmespath-java:1.12.777=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-annotations-jvm:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-annotations:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-api-jvm:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-api:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-mpp-utils-jvm:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-mpp-utils:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-normalized-cache-api-jvm:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-normalized-cache-api:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-normalized-cache-jvm:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-normalized-cache:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-runtime-jvm:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-runtime:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.apollographql.apollo3:apollo-rx3-support:3.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.atilika.kuromoji:kuromoji-core:0.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.atilika.kuromoji:kuromoji-ipadic:0.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.benasher44:uuid-jvm:0.3.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.benasher44:uuid:0.3.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.beust:jcommander:1.47=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.carrotsearch:hppc:0.8.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.datadoghq:dd-trace-api:1.45.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.datadoghq:java-dogstatsd-client:2.8.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.eclipsesource.minimal-json:minimal-json:0.9.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.ecwid.consul:consul-api:1.4.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.ethlo.time:itu:1.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.core:jackson-annotations:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.core:jackson-core:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.core:jackson-databind:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-smile:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.datatype:jackson-datatype-guava:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.datatype:jackson-datatype-joda:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.datatype:jackson-datatypes-collections:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.jackson:jackson-bom:2.17.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml.uuid:java-uuid-generator:5.1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.fasterxml:classmate:1.5.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.ben-manes.caffeine:caffeine:2.9.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.davidmoten:geo:0.8.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.davidmoten:grumpy-core:0.2.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.f4b6a3:uuid-creator:5.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jffi:1.3.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-a64asm:1.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-constants:0.9.8=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-enxio:0.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-ffi:2.2.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-posix:3.0.35=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-unixsocket:0.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.jnr:jnr-x86asm:1.0.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.luben:zstd-jni:1.5.6-3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.seancfoley:ipaddress:5.4.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.spullara.mustache.java:compiler:0.9.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.victools:jsonschema-generator:4.31.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.victools:jsonschema-module-jackson:4.31.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.github.vladimir-bukhtoyarov:bucket4j-core:4.7.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.android:annotations:*******=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.api.grpc:proto-google-common-protos:2.41.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.auth:google-auth-library-credentials:1.23.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.auto.value:auto-value-annotations:1.11.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.cloud.opentelemetry:detector-resources-support:0.29.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.code.findbugs:annotations:3.0.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.code.findbugs:jsr305:3.0.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.code.gson:gson:2.13.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.errorprone:error_prone_annotations:2.38.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.flatbuffers:flatbuffers-java:24.3.25=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.googlejavaformat:google-java-format:1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.guava:failureaccess:1.0.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.guava:guava:33.3.1-jre=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.inject.extensions:guice-assistedinject:5.1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.inject:guice:5.1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.j2objc:j2objc-annotations:3.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.protobuf:protobuf-java-util:3.25.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.protobuf:protobuf-java:3.25.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.re2j:re2j:1.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.google.template:soy:2012-12-21.1.indeed.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.googlecode.concurrent-trees:concurrent-trees:2.4.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.googlecode.efficient-java-matrix-library:ejml:0.23=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.googlecode.libphonenumber:libphonenumber:8.13.49=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.googlecode.protobuf-java-format:protobuf-java-format:1.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.graphql-java:graphql-java:19.11=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.graphql-java:java-dataloader:3.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.hankcs:hanlp:portable-1.8.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.hubspot.jackson:jackson-datatype-protobuf:0.9.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.ibm.icu:icu4j:76.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed.legacy:lucenekorean:0.1.21=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed.salary:snippet-service-client:5.0.163=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed.salary:snippet-service-common:5.0.163=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:acme-dossier-service:5.0.141=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:ads-apply-start-common:5.0.11=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:adsci-stream-common-proto:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:akatsuki:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:any-common:5.0.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:bets-common:9999.eldarg.live.20250723.221552.589738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:bid-scoring-service:5.0.328=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:bidalgos-adviab-lib:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:bidding-common:5.0.111=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:bidding-service:5.0.276=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:boxcar-legacy:5.0.235=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:branded-ads-manager-service:5.0.430=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:budget-optimizer:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:campaign-ownership-protos:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:campaign-stream-common-protos:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:cloudflare-utils:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-canary-core:9999.eldarg.live.20250728.124251.622134=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-executors:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-identification:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-idkey:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-resiliency:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-core:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-formatting:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-indeedapply:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-model:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-salaryconfig:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-shingles:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-text:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:common-search-waldo:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:communications-hub-api-gateway:5.0.627=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:config-manager-common:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:config-manager-internal:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:config-manager:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:content-type-registry-service:5.0.176=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:daemonized-worker-shim:5.0.67=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:docservice-protos:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:dradis-common-s3:5.0.69=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:employer-notification-center-api:5.0.609=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:employer-notification-center-protos:9999.eldarg.live.20250616.130513.009593=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:empty:5.0.16=runtimeClasspath,runtimeClasspath,compileClasspath,compileClasspath,testRuntimeClasspath,testRuntimeClasspath,testCompileClasspath,testCompileClasspath
com.indeed:feature-management-common:9999.eldarg.live.20250707.152334.192613=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:file-service-client:9999.eldarg.live.20250623.011020.763001=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:format-dates:9999.eldarg.live.20250731.114629.605322=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:format-locations:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:graphql-common-types:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:graphql-exceptions:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:graphql-scalars:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:grpc-api-sli-interceptors:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:grpc-native-client:9999.eldarg.live.20250728.194115.965423=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:grpc-native-proto:9999.eldarg.live.20250728.194115.965423=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:grpc-native-util:9999.eldarg.live.20250728.194115.965423=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:hive-thrift-compatibility-hack:5.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indapply-api-client-common:9999.eldarg.live.20250623.011020.763001=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indapply-common-interview:5.1.318=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-apollo-onegraph-client-spring-boot-starter:5.0.83=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-health-adapter-spring-boot-starter:5.0.79=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-micrometer-logrepo-spring-boot-starter:5.0.72=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-private-tools-spring-boot-starter-api:5.0.94=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-private-tools-spring-boot-starter:5.0.88=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-privileged-ips-api:5.0.44=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-privileged-ips-rad-spring-boot-starter:5.0.76=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-profile-graphql-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-rest-common-model:9999.eldarg.live.20250630.010703.379453=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-spring-boot-starter-test:5.0.350=testRuntimeClasspath,testCompileClasspath
com.indeed:indeed-spring-boot-starter:5.0.350=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:inline-bidder-common:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:interview-now-rad-client:5.0.109=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:java-dogstatsd-client-199-workaround:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:job-level-bid-service:5.0.137=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:job-level-bids-common-protos:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:job-level-bids-common:9999.eldarg.live.20250728.034317.002708=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:job-scoring-interface:5.0.42=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:job-search-context:5.0.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:jobsearch-query-common:5.0.72=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:js-query-interpretation-service-api:5.0.705=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:js-query-interpretation-service:5.0.705=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:jsbe-shared-query-proto:5.0.14=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-common-datatypes-with-common-protos:5.0.8=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-common-datatypes:5.0.27=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-common-metrics-core:5.0.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-common-metrics-legacy-boxcar:5.0.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-common-protos-flat:5.0.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-common-protos:5.0.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-matcher-proto:5.1.88=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-relevance-common-artifact:9999.eldarg.live.20250731.034832.999957=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-relevance-common-commute:9999.eldarg.live.20250731.034832.999957=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-relevance-common-location-overrides:9999.eldarg.live.20250731.034832.999957=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:location-relevance-common-protos:9999.eldarg.live.20250731.034832.999957=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:log4j2-spring-boot-starter:5.0.27=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:log4j2-spring:5.0.27=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-client:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-common:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-dogstatsd:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-events:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-layout:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-log4j2:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-metrics:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-opsappinfo:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-repos:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-servicecheck:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-stats:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logging-uid:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logrepo-annotations-log4j:5.0.78=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logrepo-annotations:5.0.78=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:logrepo-avro-spring-boot-starter:5.0.78=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:maggreg8-dogstatsd:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:maggreg8-indeed-spring:9999.eldarg.live.20250707.152334.192613=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:maggreg8-indeed:9999.eldarg.live.20250707.152334.192613=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:maggreg8:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:match-comparison-engine:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:mdp-data-catalog-core:5.0.1176=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:mdp-data-catalog:5.0.1176=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:mdp-present-api:5.0.1158=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:messaging-gateway-service:5.0.144=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:metadata-client-artifact-client:5.0.128=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:metadata-client-artifact:5.0.128=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:micrometer-registry-maggreg8-spring-boot-starter:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:micrometer-registry-maggreg8:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:niffler-api-common:5.0.1359=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:niffler-common-proto:5.0.1359=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:niffler-job-feature-service-client:9999.eldarg.live.20250723.221552.589738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:normalized-candidate-data-api-grpc:5.0.44=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:nrt-event-tracking-service-rpclistener-micrometer:5.0.272=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:nrt-event-tracking-service:5.0.272=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:nrt-proto:5.0.14=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:occusims-artifact:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:pay-core-data-logging:9999.eldarg.live.20250730.053623.023598=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:pay-job-format-composite-salary:9999.eldarg.live.20250730.053623.023598=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:pay-job-format-core:9999.eldarg.live.20250730.053623.023598=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:pay-job-format-job-tag:9999.eldarg.live.20250730.053623.023598=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:pay-job-format-mdp-core-pay-data:9999.eldarg.live.20250730.053623.023598=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:placementid-client:9999.eldarg.live.20250729.062149.362825=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:placementid-data-types:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:proctor-ant-plugin:5.0.22=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:proctor-codegen:5.0.22=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:proctor-common:5.3.29=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:proctor-consumer:5.3.29=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:profile-api-model:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:profile-consumer:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:profile-definition:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:profile-implementation:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:proto2-validate:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:protobuf-util-fieldmask:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:qualification-feedback-service:5.0.473=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:ranking-orchestrator-service-client:5.0.164=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:recommend-common-continent:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:recommend-common-proto:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:relevance-context-common-proto:9999.eldarg.live.20250731.034832.999957=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:relevance-filters:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:scheduled-worker-spring-boot-starter:5.0.87=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:search-and-recommendation-cost-attribution:5.0.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:search-insight-common:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:segment-selector-common:5.0.24=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:slf4j2-spark-compatibility-hack:5.0.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:smee-client:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:smee-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:soa-java-authentication:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:soa-java-authorization:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:soa-java-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:soa-java-mesh-resources:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:soa-java-service-resolver:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:sponjobs:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:status-core:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:status-web:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:stitched-ranking-dsl:5.0.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:suggestions-spring-boot-starter:5.0.74=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:synset-common:9999.eldarg.live.20250729.062149.362825=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:tiered-config-spring-boot-starter:5.0.69=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:top-applicant-poa-threshold-client:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:upcharge-opt-in-client:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:upcharge-rad-client:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:updog-rezservice-client:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:user-agent-utils:5.0.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:user-personalization-api:5.0.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-annotations:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-base:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-bytes:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-core:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-crypto:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-distance:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-hex:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-io:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-log:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-mmap:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-numeric:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-pagination:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-pair:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-retry:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-serialization:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-shell:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-strings:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-time:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-timing:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-unsafe:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-varexport:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:util-xml-dump:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:vector-index-service:9999.eldarg.live.20250721.204722.840542=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.indeed:vector-search-service:5.0.146=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.io7m.xom:xom:1.2.10=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.jamesmurty.utils:java-xmlbuilder:0.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.jayway.jsonpath:json-path:2.7.0=testRuntimeClasspath,testCompileClasspath
com.jcraft:jsch:0.1.42=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.lmax:disruptor:4.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.martiansoftware:jsap:2.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.maxmind.db:maxmind-db:2.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.maxmind.geoip2:geoip2:2.15.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.maxmind.geoip:geoip-api:1.2.14=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.mysql:mysql-connector-j:8.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.netflix.graphql.dgs:graphql-error-types:5.6.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.netflix.hystrix:hystrix-core:1.5.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.networknt:json-schema-validator:1.3.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.rabbitmq:amqp-client:3.5.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.okhttp3:logging-interceptor:4.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.okhttp3:okhttp:4.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.okio:okio-jvm:3.6.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.okio:okio:3.6.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.retrofit2:converter-jackson:2.11.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.retrofit2:converter-scalars:2.11.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.retrofit2:retrofit:2.11.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.retrofit:converter-jackson:1.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup.retrofit:retrofit:1.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.squareup:javapoet:1.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.sun.activation:javax.activation:1.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.sun.istack:istack-commons-runtime:3.0.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.sun.jersey:jersey-core:1.19.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.sun.jersey:jersey-json:1.19.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.sun.jersey:jersey-server:1.19.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.sun.mail:javax.mail:1.6.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.tdunning:t-digest:3.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.thoughtworks.xstream:xstream:1.4.20=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.unboundid.product.scim2:scim2-sdk-common:3.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.vaadin.external.google:android-json:0.0.20131108.vaadin1=testRuntimeClasspath,testCompileClasspath
com.worksap.nlp:jdartsclone:1.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
com.worksap.nlp:sudachi:0.7.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-beanutils:commons-beanutils:1.9.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-cli:commons-cli:1.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-codec:commons-codec:1.17.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-collections:commons-collections:3.2.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-configuration:commons-configuration:1.10=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-dbcp:commons-dbcp:1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-digester:commons-digester:2.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-discovery:commons-discovery:0.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-el:commons-el:1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-fileupload:commons-fileupload:1.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-httpclient:commons-httpclient:3.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-io:commons-io:2.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-lang:commons-lang:2.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-net:commons-net:3.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-pool:commons-pool:1.5.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
commons-validator:commons-validator:1.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
cz.jirutka.unidecode:unidecode:1.0.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
de.jollyday:jollyday:0.4.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
de.undercouch:bson4jackson:2.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
dk.brics.automaton:automaton:1.11-8=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
dk.brics:automaton:1.12-4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
dnsjava:dnsjava:2.1.8=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
edu.stanford.nlp:stanford-corenlp:3.4.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:acme-common-encryption:9999.eldarg.live.20250730.080953.478173=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:adcentral-proto:5.1.79=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ads-boxcar-util:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ads-common:5.1.102=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ads-log-value-buckets:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ads-protos:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:adsystem-reporting-common:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:advertisementservice:5.1.161=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:advertiserservice:5.1.207=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:aggcommon-core:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:aggcommon-persistence:5.1.75=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:aggcommon-proto:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:aggcommon-util:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:beaker:5.1.74=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:blobby-service:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:boxcar-method:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:butterfly-modelapi-tensorflow:5.1.393=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:butterfly-modelapi:5.1.393=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:butterfly-tensorflow_2.14.1:5.0.24=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:campaign-common:9999.eldarg.live.20250728.034317.002708=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:campaign-stream-common:5.2.54=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:casper-plan-service:5.0.184=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:click-thru-service-client:9999.eldarg.live.20250603.021235.759210=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-artifacts:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-cli:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-concurrent:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-consul:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-daemons:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-dbutil:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-decisiontree:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-demeter:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-functional:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-gettext:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-hystrix-datadog:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-hystrix:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-job-update-queue:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-job:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-jobsearch:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-jobtag:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-json:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-lucene3-shade:5.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-mail:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-message-rabbit:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-message:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-metadata:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-model:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-money:5.1.51=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-net:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-nlp:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-protobuf:9999.eldarg.live.20250630.010703.379453=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-salary:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-search:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-slo-boxcar:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-slo-okhttp:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-slo:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-soy:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-spring:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-stats:9999.eldarg.live.20250602.080603.649806=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-streams:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-strings:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-timebomb:5.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-tracing-db:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-useragent:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:common-util:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:commongo:5.1.137=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:congo:5.1.124=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:docservice:9999.eldarg.live.20250623.155018.203984=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dradis-adv-moderation-service:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dradis-common-encryption:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dradis-common:9999.eldarg.live.20250731.114629.605322=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dradis-data:9999.eldarg.live.20250731.114629.605322=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dradis-moderation-common:9999.eldarg.live.20250731.114629.605322=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dremr-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dremr-service:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:dundee:5.0.90=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:employer-activity-client:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:employerservice:5.1.26=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:extraction-jobtype:5.1.10=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:franklin:5.1.36=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:gettext-commons-forked:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:horton:5.1.506=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:identity-client:5.1.349=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:indapply-common:5.1.318=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:iplocation:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:iq-service-client:5.11.885=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:iq-service-utilities:5.11.885=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ita-client-shared:5.1.51=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ita-common-budget:5.1.43=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ita-common:5.1.209=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:job-rate-publisher-common:5.0.72=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:job-seeker-notification-service:5.1.119=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:job-state-change-common:5.0.47=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobanalysis:9999.eldarg.live.20250731.020111.149350=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearch-proto:5.1.235=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearch:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice-concurrent:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice-countryconfig:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice-location:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice-logitems:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice-pagination:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice-thresholding:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobsearchservice:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jobstateservice:5.1.45=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:jsbe-dynamodb:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:location-common-metrics:5.0.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:location-core:5.1.63=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:location-geocoder:5.1.86=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:location-matcher:5.1.88=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:logging:5.1.147=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:logproc:5.1.779=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:logrepo-client:5.1.20=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:match-factors:5.0.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:metadata-service-proto:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:metadata-service:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:mindy-common:9999.eldarg.live.20250731.024735.120937=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:mopsy:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:narcissus:5.1.19=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:nigma-common:5.1.98=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:nigma:5.1.75=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notification-center-common:9999.eldarg.live.20250317.034121.417327=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notification-preferences-common:5.0.141=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common-constants:5.1.193=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common-phonenumberutils:5.1.193=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common-protos:5.1.194=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common-queues:5.1.193=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common-sieve:5.1.193=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common-status:5.1.193=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-common:5.1.193=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:notifications-publication-service:5.2.28=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:osiris-client:5.2.70=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:osiris-common:5.2.70=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:post:5.2.62=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:proctor-internal:5.3.29=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:proggle-config:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:protobuf-options:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:pushcreds-client:5.1.36=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rabbitmq-admin:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rad-client:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:railgun-common:5.1.64=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:ranking:5.2.738=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:raven-common-client:9999.eldarg.live.20250603.021235.759210=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:raven-common-server:9999.eldarg.live.20250603.021235.759210=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:raven-delivery-client:5.1.120=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:raven-delivery-common:5.1.120=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:recommend-common:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:resume-parsing-service:5.0.442=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rez-candidate-preferences-service:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rez-contact-data-service:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rez-employer-preferences-service:5.1.139=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rezmodel:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:reznormalization:5.1.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rezwebapp-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:rosetta:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:salary-override-tools:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:salary-urls:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:savedjobsservice:5.1.47=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:search-config:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:secret-client:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:sen:5.1.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:serialized-search-criteria-models:5.0.46=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:serialized-search-criteria:5.0.46=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:soa-feign:9999.eldarg.live.20250707.152334.192613=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:soa-grpc-java:5.0.235=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:soa-java-client:9999.eldarg.live.20250728.124251.622134=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:soa-java:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:soa-retrofit:9999.eldarg.live.20250728.124251.622134=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:sourceservice-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:spring-cron:5.1.85=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-classifiers:5.1.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-curious-george-common:9999.eldarg.live.20250729.062149.362825=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-labeler-common:5.1.13=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-mmap:5.1.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-protoloader:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:squall-waldo-common:5.1.269=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:stacy:9999.eldarg.live.20250729.013729.650116=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:subway-common:5.1.143=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:tip-api:5.1.49=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:tip-ip-common:5.2.22=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:tip-ip-service:5.0.31=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:tip-url-common:9999.eldarg.live.20250623.011020.763001=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:titlenorm-core:9999.eldarg.live.20250721.010545.801958=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:titlenorm-service:5.1.62=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:updog-rezalerts:5.1.58=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:updog-rezcommon:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:updog-rezparser:5.1.208=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:url-tip:5.1.35=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:urlrewrite:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:user-profile-common:5.0.335=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:userservice:5.1.106=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-artifactutil:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-common-constants:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-common-data:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-common-index:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-common-util:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-proctor:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-proto:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-query:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-ranking:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex-rule:5.1.759=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:waldojobindex:5.1.758=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:warmup:9999.eldarg.live.20250728.010839.293768=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
indeed:webapp-common:9999.eldarg.live.20250731.091926.751940=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:common-utils:7.7.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:kafka-avro-serializer:7.7.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:kafka-schema-registry-client:7.7.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:kafka-schema-serializer:7.7.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:kafka-streams-avro-serde:7.7.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:logredactor-metrics:1.0.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.confluent:logredactor:1.0.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.openfeign:feign-core:11.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.openfeign:feign-httpclient:11.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.openfeign:feign-jackson:11.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.openfeign:feign-okhttp:11.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.openfeign:feign-slf4j:11.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-all:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-annotations:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-bulkhead:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-cache:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-circuitbreaker:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-circularbuffer:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-consumer:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-core:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-framework-common:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-micrometer:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-ratelimiter:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-retry:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-spring-boot2:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-spring:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.resilience4j:resilience4j-timelimiter:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.url-detector:url-detector:0.1.23=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.github.x-stream:mxparser:1.2.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-all:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-api:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-auth:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-context:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-core:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-gcp-csm-observability:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-grpclb:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-inprocess:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-netty-shaded:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-netty:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-okhttp:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-opentelemetry:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-protobuf-lite:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-protobuf:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-rls:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-services:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-servlet-jakarta:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-servlet:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-stub:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.grpc:grpc-util:1.68.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.micrometer:micrometer-commons:1.14.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.micrometer:micrometer-core:1.14.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.micrometer:micrometer-observation:1.14.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-buffer:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-codec-http2:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-codec-http:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-codec-socks:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-codec:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-common:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-handler-proxy:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-handler:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-resolver:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-transport-classes-epoll:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-transport-native-unix-common:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty-transport:4.1.114.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.netty:netty:3.7.0.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry.contrib:opentelemetry-gcp-resources:1.36.0-alpha=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry.semconv:opentelemetry-semconv:1.25.0-alpha=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-api-incubator:1.40.0-alpha=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-api:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-context:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk-common:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk-extension-autoconfigure:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk-logs:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk-metrics:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk-trace:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentelemetry:opentelemetry-sdk:1.40.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentracing:opentracing-api:0.33.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentracing:opentracing-noop:0.33.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.opentracing:opentracing-util:0.33.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.perfmark:perfmark-api:0.27.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.prestosql:presto-jdbc:343=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.qameta.allure:allure-java-commons:2.29.0=testRuntimeClasspath
io.qameta.allure:allure-junit-platform:2.29.0=testRuntimeClasspath
io.qameta.allure:allure-junit5:2.29.0=testRuntimeClasspath
io.qameta.allure:allure-model:2.29.0=testRuntimeClasspath
io.qameta.allure:allure-test-filter:2.29.0=testRuntimeClasspath
io.qameta.allure:allure-testng:2.29.0=testRuntimeClasspath
io.reactivex.rxjava3:rxjava:3.1.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.reactivex:rxjava:1.1.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.searchbox:jest-common:0.1.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.searchbox:jest:0.1.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.springfox:springfox-core:2.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.springfox:springfox-schema:2.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.springfox:springfox-spi:2.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.springfox:springfox-spring-web:2.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.springfox:springfox-swagger-common:2.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.springfox:springfox-swagger2:2.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.swagger.core.v3:swagger-annotations:2.2.28=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.swagger.core.v3:swagger-core:2.2.20=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.swagger.core.v3:swagger-models:2.2.20=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.swagger:swagger-annotations:1.5.10=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.swagger:swagger-models:1.5.10=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
io.trino:trino-jdbc:444=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
it.unimi.dsi:dsiutils:2.3.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
it.unimi.dsi:fastutil:8.5.15=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
it.unimi.dsi:sux4j:4.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
jakarta.activation:jakarta.activation-api:2.1.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
jakarta.annotation:jakarta.annotation-api:3.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
jakarta.servlet:jakarta.servlet-api:5.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
jakarta.xml.bind:jakarta.xml.bind-api:4.0.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.annotation:javax.annotation-api:1.3.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.cache:cache-api:1.1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.inject:javax.inject:1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.interceptor:javax.interceptor-api:1.2.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.servlet.jsp:javax.servlet.jsp-api:2.3.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.servlet:javax.servlet-api:4.0.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.validation:validation-api:2.0.1.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.ws.rs:javax.ws.rs-api:2.1.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
javax.xml.bind:jaxb-api:2.3.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
joda-time:joda-time:2.13.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.bytebuddy:byte-buddy-agent:1.15.4=testRuntimeClasspath,testCompileClasspath
net.bytebuddy:byte-buddy:1.15.4=testRuntimeClasspath,testCompileClasspath
net.htmlparser.jericho:jericho-html:3.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.java.dev.jets3t:jets3t:0.9.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.java.dev.jna:jna:5.14.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.jcip:jcip-annotations:1.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.minidev:accessors-smart:2.4.7=testRuntimeClasspath,testCompileClasspath
net.minidev:json-smart:2.4.7=testRuntimeClasspath,testCompileClasspath
net.sf.jopt-simple:jopt-simple:5.0.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.sf.trove4j:trove4j:3.0.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
net.spy:spymemcached:2.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
nl.basjes.collections:prefixmap:2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
nl.basjes.parse.useragent:yauaa:7.26.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.abego.treelayout:org.abego.treelayout.core:1.0.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.abstractj.kalium:kalium:0.7.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.antlr:ST4:4.3.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.antlr:antlr-runtime:3.5.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.antlr:antlr4-runtime:4.13.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.antlr:antlr4:4.13.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.ant:ant-launcher:1.10.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.ant:ant:1.10.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.arrow:arrow-compression:17.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.arrow:arrow-format:17.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.arrow:arrow-memory-core:17.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.arrow:arrow-memory-netty-buffer-patch:17.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.arrow:arrow-memory-netty:17.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.arrow:arrow-vector:17.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.avro:avro-compiler:1.11.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.avro:avro:1.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.axis:axis-jaxrpc:1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.axis:axis-saaj:1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-collections4:4.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-compress:1.27.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-csv:1.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-jexl3:3.4.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-jexl:2.1.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-lang3:3.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-math3:3.6.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-math:2.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-pool2:2.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.commons:commons-text:1.12.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.curator:curator-client:2.7.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.curator:curator-framework:2.7.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.curator:curator-recipes:2.7.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.directory.api:api-asn1-api:1.0.0-M20=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.directory.api:api-util:1.0.0-M20=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.directory.server:apacheds-i18n:2.0.0-M15=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.directory.server:apacheds-kerberos-codec:2.0.0-M15=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.hadoop:hadoop-annotations:2.6.0-cdh5.16.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.hadoop:hadoop-auth:2.6.0-cdh5.16.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.hadoop:hadoop-common:2.6.0-cdh5.16.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.htrace:htrace-core4:4.0.1-incubating=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents.client5:httpclient5:5.4.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents.core5:httpcore5-h2:5.3.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents.core5:httpcore5:5.3.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents:fluent-hc:4.5.14=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents:httpasyncclient:4.1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents:httpclient:4.5.14=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents:httpcore-nio:4.4.12=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.httpcomponents:httpcore:4.4.16=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.kafka:kafka-clients:3.7.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.kafka:kafka-streams:3.7.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.logging.log4j:log4j-1.2-api:2.24.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.logging.log4j:log4j-api:2.24.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.logging.log4j:log4j-core:2.24.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.logging.log4j:log4j-layout-template-json:2.24.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.logging.log4j:log4j-slf4j2-impl:2.24.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-analyzers-common:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-analyzers:2.4.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-backward-codecs:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-core:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-grouping:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-highlighter:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-join:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-memory:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-misc:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-queries:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-queryparser:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-sandbox:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-spatial3d:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.lucene:lucene-suggest:8.11.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.pdfbox:fontbox:2.0.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.pdfbox:pdfbox:2.0.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.taglibs:taglibs-standard-impl:1.2.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.thrift:libthrift:0.20.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.tomcat.embed:tomcat-embed-el:9.0.83=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.tomcat:tomcat-jasper-el:7.0.109=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.velocity:velocity-engine-core:2.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apache.zookeeper:zookeeper:3.4.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.apiguardian:apiguardian-api:1.1.2=testRuntimeClasspath,testCompileClasspath
org.aspectj:aspectjweaver:1.9.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.assertj:assertj-core:3.22.0=testRuntimeClasspath,testCompileClasspath
org.atilika.kuromoji:kuromoji:0.7.7=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.bouncycastle:bcprov-jdk18on:1.79=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.checkerframework:checker-qual:3.48.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.codehaus.jackson:jackson-core-asl:1.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.codehaus.jackson:jackson-jaxrs:1.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.codehaus.jackson:jackson-mapper-asl:1.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.codehaus.jackson:jackson-xc:1.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.codehaus.jettison:jettison:1.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.codehaus.mojo:animal-sniffer-annotations:1.24=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.commands:3.6.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.contenttype:3.4.100=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.expressions:3.4.300=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.filesystem:1.3.100=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.jobs:3.5.100=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.resources:3.7.100=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.core:org.eclipse.core.runtime:3.7.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.equinox:org.eclipse.equinox.app:1.3.100=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.equinox:org.eclipse.equinox.common:3.6.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.equinox:org.eclipse.equinox.preferences:3.4.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.equinox:org.eclipse.equinox.registry:3.5.101=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.jdt:org.eclipse.jdt.core:3.10.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.osgi:org.eclipse.osgi:3.7.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.eclipse.text:org.eclipse.text:3.5.101=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.client:elasticsearch-rest-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.client:elasticsearch-rest-high-level-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.client:transport:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:aggs-matrix-stats-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:lang-mustache-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:mapper-extras-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:parent-join-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:percolator-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:rank-eval-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:reindex-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch.plugin:transport-netty4-client:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-cli:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-core:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-geo:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-lz4:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-plugin-classloader:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-secure-sm:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-ssl-config:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch-x-content:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.elasticsearch:elasticsearch:7.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.foundationdb:fdb-java:5.2.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.freemarker:freemarker:2.3.30=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.functionaljava:functionaljava:3.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.glassfish.jaxb:jaxb-runtime:2.3.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.glassfish.jaxb:txw2:2.3.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.glassfish:javax.json:1.1.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.hamcrest:hamcrest:2.2=testRuntimeClasspath,testCompileClasspath
org.hdrhistogram:HdrHistogram:2.2.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.hibernate.validator:hibernate-validator:6.2.5.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.hibernate:hibernate-validator:6.2.5.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.htmlparser:htmlparser:1.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.javassist:javassist:3.18.2-GA=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jboss.logging:jboss-logging:3.4.1.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlin:kotlin-stdlib:2.0.21=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:atomicfu-jvm:0.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:atomicfu:0.17.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-reactive:1.6.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-rx3:1.6.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jetbrains:annotations:23.0.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jgrapht:jgrapht-core:0.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.joda:joda-money:1.0.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.jsoup:jsoup:1.15.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.junit.jupiter:junit-jupiter-api:5.11.2=testCompileClasspath
org.junit.jupiter:junit-jupiter-api:5.11.3=testRuntimeClasspath
org.junit.jupiter:junit-jupiter-engine:5.11.2=testCompileClasspath
org.junit.jupiter:junit-jupiter-engine:5.11.3=testRuntimeClasspath
org.junit.jupiter:junit-jupiter-params:5.11.2=testCompileClasspath
org.junit.jupiter:junit-jupiter-params:5.11.3=testRuntimeClasspath
org.junit.jupiter:junit-jupiter:5.11.2=testCompileClasspath
org.junit.jupiter:junit-jupiter:5.11.3=testRuntimeClasspath
org.junit.platform:junit-platform-commons:1.11.2=testCompileClasspath
org.junit.platform:junit-platform-commons:1.11.3=testRuntimeClasspath
org.junit.platform:junit-platform-engine:1.11.2=testCompileClasspath
org.junit.platform:junit-platform-engine:1.11.3=testRuntimeClasspath
org.junit.platform:junit-platform-launcher:1.11.3=testRuntimeClasspath
org.junit:junit-bom:5.11.2=testCompileClasspath
org.junit:junit-bom:5.11.3=testRuntimeClasspath
org.latencyutils:LatencyUtils:2.0.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.lz4:lz4-java:1.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.mapstruct:mapstruct:1.0.0.Final=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.markdownj:markdownj:0.3.0-1.0.2b4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.mockito:mockito-core:5.14.2=testRuntimeClasspath,testCompileClasspath
org.mockito:mockito-junit-jupiter:5.14.2=testRuntimeClasspath,testCompileClasspath
org.mongodb:bson-record-codec:4.11.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.mongodb:bson:4.11.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.mongodb:mongodb-driver-core:4.11.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.mongodb:mongodb-driver-sync:4.11.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.objenesis:objenesis:3.3=testRuntimeClasspath,testCompileClasspath
org.opentest4j:opentest4j:1.3.0=testRuntimeClasspath,testCompileClasspath
org.ow2.asm:asm-analysis:9.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.ow2.asm:asm-commons:9.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.ow2.asm:asm-tree:9.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.ow2.asm:asm-util:9.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.ow2.asm:asm:9.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.projectlombok:lombok:1.18.34=compileClasspath,annotationProcessor
org.reactivestreams:reactive-streams:1.0.4=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.reflections:reflections:0.9.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.rocksdb:rocksdbjni:7.9.2=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.scribe:scribe:1.3.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.skyscreamer:jsonassert:1.5.1=testRuntimeClasspath,testCompileClasspath
org.slf4j:jcl-over-slf4j:2.0.17=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.slf4j:jul-to-slf4j:2.0.17=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.slf4j:slf4j-api:2.0.17=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springdoc:springdoc-openapi-common:1.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springdoc:springdoc-openapi-ui:1.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springdoc:springdoc-openapi-webmvc-core:1.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-actuator-autoconfigure:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-actuator:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-autoconfigure:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-starter-actuator:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-starter-aop:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-starter-test:2.7.18=testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-starter-validation:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-starter:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-test-autoconfigure:2.7.18=testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot-test:2.7.18=testRuntimeClasspath,testCompileClasspath
org.springframework.boot:spring-boot:2.7.18=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework.retry:spring-retry:1.3.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-aop:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-aspects:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-beans:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-context-support:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-context:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-core:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-expression:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-jdbc:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-test:5.3.34=testRuntimeClasspath,testCompileClasspath
org.springframework:spring-tx:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-web:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.springframework:spring-webmvc:5.3.34=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.tensorflow:ndarray:0.3.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.tukaani:xz:1.9=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.webjars:swagger-ui:5.11.8=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.xerial.snappy:snappy-java:1.1.10.5=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
org.xmlunit:xmlunit-core:2.9.1=testRuntimeClasspath,testCompileClasspath
org.yaml:snakeyaml:2.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:annotations:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:apache-client:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:arns:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:auth:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:aws-core:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:aws-json-protocol:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:aws-query-protocol:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:aws-xml-protocol:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:checksums-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:checksums:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:crt-core:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:dynamodb-enhanced:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:dynamodb:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:endpoints-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:http-auth-aws-eventstream:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:http-auth-aws:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:http-auth-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:http-auth:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:http-client-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:identity-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:json-utils:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:metrics-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:netty-nio-client:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:profiles:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:protocol-core:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:regions:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:retries-spi:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:retries:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:s3:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:sdk-core:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:sqs:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:sts:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:third-party-jackson-core:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.awssdk:utils:2.29.6=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.eventstream:eventstream:1.0.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
software.amazon.payloadoffloading:payloadoffloading-common:2.2.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
xalan:serializer:2.7.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
xalan:xalan:2.7.3=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
xerces:xercesImpl:2.8.0=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
xml-apis:xml-apis:1.4.01=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
xmlenc:xmlenc:0.52=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
xmlpull:xmlpull:1.1.3.1=runtimeClasspath,compileClasspath,testRuntimeClasspath,testCompileClasspath
feature_addAllureDep=1
_=f7EuTVpbzmbg6o5MHGWCnf_OBgVXYhW-w9SjugJCJdg
_directDependenciesContent=Ol7Df1hRzNAQ7RkbyfNbQblppq0G_bTTm9872IcasWU
_timestamp=1753976163468
