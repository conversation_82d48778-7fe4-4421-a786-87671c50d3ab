plugins {
    id 'com.indeed.common'
    id 'com.indeed.package'
    id 'com.indeed.springboot'
    id 'com.indeed.proctor'
    id 'com.apollographql.apollo3' version "3.8.0"
}

dependencies {
    // Lombok
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    compile 'javax.servlet:javax.servlet-api'
    compileOnly 'com.google.code.findbugs:jsr305'

    // Indeed
    compile 'com.indeed:qualification-feedback-service'
    compile 'com.indeed:common-identification'
    compile 'indeed:advertiserservice'
    compile 'indeed:common-artifacts'
    compile 'indeed:dradis-adv-moderation-service'
    compile 'indeed:dradis-data'
    compile 'indeed:dradis-moderation-common'
    compile 'indeed:jobsearchservice'
    compile 'indeed:proctor-internal'
    compile 'indeed:rad-client'
    compile 'indeed:raven-delivery-client'
    compile 'indeed:raven-delivery-common'
    // comms-hub
    compile 'com.indeed:communications-hub-api-gateway'
    compile 'indeed:spring-cron'
    compile 'indeed:squall-common'
    compile 'indeed:squall-waldo-common'
    compile 'com.indeed:status-core'
    compile 'com.indeed:indeed-spring-boot-starter'
    testCompile 'com.indeed:indeed-spring-boot-starter-test'

    // Third Party
    compile "com.apollographql.apollo3:apollo-runtime:3.8.0"
    compile "com.apollographql.apollo3:apollo-rx3-support:3.8.0"

    // Test Only
    testCompile 'junit:junit'
    testCompile 'org.mockito:mockito-core'
    testCompile 'org.springframework:spring-test'

    compile 'com.indeed:indeed-apollo-onegraph-client-spring-boot-starter'
}

indeedProctor {
    add {
        outputPackageName = 'com.indeed.job.is.live.audience.builder.groups'
        outputSpecPath = 'proctor/proctor-specification.json'
        outputGroupsClass = 'JobIsLiveProctorGroups'
        outputGroupsManagerClass = 'JobIsLiveProctorGroupsManager'
        outputContextClass = 'JobIsLiveProctorContext'
    }
}

configurations.compile {
    // default testing framework is JUnit5
    exclude group: 'junit', module: 'junit'
    exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    
    // exclude the web server
    exclude group: 'com.indeed', module: 'jetty-h2c-spring-boot-starter'
}

run {
    main = 'com.indeed.job.is.live.audience.builder.JobIsLiveAudienceBuilder'
}

apollo {
    service("oneGraph") {
        schemaFile.set(file("src/main/graphql/com/indeed/one/graph/client/schema.graphqls"))
        packageName.set("com.indeed.one.graph.client")
        srcDir("src/main/graphql/com/indeed/one/graph/client")

        // Configure the introspection to download the schema
        introspection {
            endpointUrl.set("https://apis.qa.indeed.tech/graphql")
        }
    }
}

generateOneGraphApolloSources.dependsOn 'downloadOneGraphApolloSchemaFromIntrospection'

apply from: 'jacocoConfig.gradle'