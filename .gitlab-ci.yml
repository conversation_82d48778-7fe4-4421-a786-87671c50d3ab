# New projects should use Gitlab CI.  You may need to take some steps to
# configure your new project to use Gitlab CI, such as enabling the shared
# runners.  For more details, see: https://wiki.indeed.com/x/TRozG
include:
  - project: pipeline/gitlab-pipeline
    file: /templates/java-golden.yml

variables:
  HAS_DEPLOYABLE: "true"
  SKIP_DEPLOY_CHECKS: "true" # Deploy checks are for marvin deployables and should be skipped for cron jobs
  QA_DEPLOY_URL: "https://orc.qa.indeed.tech/tasktemplate?q=job-is-live/JobIsLiveAudienceBuilder"
  PROD_DEPLOY_URL: "https://orc.indeed.tech/tasktemplate?q=job-is-live/JobIsLiveAudienceBuilder"
  LEMMA: "false"
  PRODUCT_GROUP: "job-is-live"
  ARC_NAMESPACE : "job-is-live"
