apply plugin: 'jacoco'
// Ref: https://docs.gradle.org/current/userguide/jacoco_plugin.html

// Exclude the following patterns from coverage analysis (both reports and enforcement):
def jacocoExcludes = [
        // adh
        "com/indeed/job/is/live/audience/builder/adh/**",
        // config
        "com/indeed/job/is/live/audience/builder/config/**",
        // enums
        "com/indeed/job/is/live/audience/builder/enums/**",
        // groups
        "com/indeed/job/is/live/audience/builder/groups/**",
        // models
        "com/indeed/job/is/live/audience/builder/models/**",
        // exceptions
        "com/indeed/job/is/live/audience/builder/exceptions/**",
        // main function is hard to mock and does not benefit from coverage
        "com/indeed/job/is/live/audience/builder/JobIsLiveAudienceBuilder*",
        // one graph
        "com/indeed/one/graph/client/**",

]

def jacocoClasses = jacocoTestReport.classDirectories.files.collect {
    fileTree(dir: it, excludes: jacocoExcludes)
}

jacocoTestReport {
    reports {
        xml.destination file("${buildDir}/coverage/coverage_jacoco.xml")
    }
    afterEvaluate {
        classDirectories.setFrom(jacocoClasses)
    }
}

jacocoTestCoverageVerification {
    afterEvaluate {
        classDirectories.setFrom(jacocoClasses)
    }
    violationRules {
        rule {
            limit {
                counter = 'INSTRUCTION'  // default counter type, better than counting lines
                minimum = .01 // Minimum total coverage ratio of instructions
            }
            limit {
                counter = 'BRANCH'
                minimum = .01 // Minimum total coverage ratio of branches
            }
        }
    }
}

check {
    dependsOn jacocoTestReport
    dependsOn jacocoTestCoverageVerification
}
