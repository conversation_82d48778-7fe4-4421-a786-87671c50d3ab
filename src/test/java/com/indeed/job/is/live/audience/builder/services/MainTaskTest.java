package com.indeed.job.is.live.audience.builder.services;

import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos;
import com.indeed.dradis.data.util.AdvertiserServiceUtil;
import com.indeed.job.is.live.audience.builder.config.MetricsEmitter;
import com.indeed.job.is.live.audience.builder.config.WaldoQueryConfig;
import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.groups.JobIsLiveProctorGroups;
import com.indeed.job.is.live.audience.builder.models.JobActivityInfo;
import com.indeed.job.is.live.audience.builder.models.TaskArguments;
import com.indeed.job.is.live.audience.builder.util.CommsHubService;
import com.indeed.job.is.live.audience.builder.util.CompanySizeFilterService;
import com.indeed.job.is.live.audience.builder.util.CompanySizeSegmentService;
import com.indeed.job.is.live.audience.builder.util.DocServiceUtil;
import com.indeed.job.is.live.audience.builder.util.EmployerJobUtil;
import com.indeed.job.is.live.audience.builder.util.JobUrlUtil;
import com.indeed.job.is.live.audience.builder.util.ProctorUtil;
import com.indeed.job.is.live.audience.builder.util.TimeUtil;
import com.indeed.logging.client.LogEntry;
import com.indeed.logging.client.LogEntryFactory;
import com.indeed.logging.client.stats.StatsEmitter;
import com.indeed.one.graph.client.EmployerJobQuery;
import com.indeed.squall.waldocommon.WaldoClient;
import com.indeed.squall.waldocommon.apibuilder.AggregationQuery;
import com.indeed.squall.waldocommon.model.api.AggregationQueryResponse;
import com.indeed.util.core.Pair;
import com.indeed.waldojobindex.search.refineby.RefineByType;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

import java.io.File;
import java.time.Clock;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_JP_FTP;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_JP_PTP;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_US_FTP;
import static com.indeed.job.is.live.audience.builder.services.MainTask.JOB_IS_LIVE_EMAIL;
import static com.indeed.job.is.live.audience.builder.util.RavenIdempotencyKey.getIdempotencyKeyWithJobId;
import static com.indeed.job.is.live.audience.builder.util.TimeUtil.DEFAULT_TIME_ZONE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MainTaskTest {
    private static final TaskArguments DEFAULT_TASK_ARGUMENTS = TaskArguments.builder()
            .jobIsLiveLookbackDays(7)
            .adhTestRun(true)
            .hqmQaTestRun(false)
            .testHqmAdvertiserIds(new HashSet())
            .filterWaldoEmailsAlreadySent(true)
            .newWaldoQuery(true)
            .build();
    public static final long START_TIME = 0L;

    @Mock
    private CommsHubService commsHubService;

    @Mock
    private LogEntryFactory logEntryFactory;

    @Mock
    private ProctorUtil proctorUtil;

    @Mock
    private StatsEmitter statsEmitter;

    @Mock
    private AdvertiserServiceUtil advertiserServiceUtil;

    @Mock
    private JobUrlUtil jobUrlUtil;

    @Mock
    private WaldoClient waldoClient;

    @Mock
    private JobIsLiveProctorGroups mockProctorGroups;

    @Mock
    private JobIsLiveProctorGroups mockProctorGroupsCommsHub;

    @Mock
    private LogEntry mockLogEntry;

    @Mock
    private File mockFile;

    @Mock
    private DocServiceUtil docServiceUtil;

    @Mock
    private Clock clock;

    @Mock
    private TimeUtil timeUtil;

    @Mock
    private MetricsEmitter metricsEmitter;

    @Mock
    private WaldoQueryConfig waldoQueryConfig;

    @Mock
    private EmployerJobUtil employerJobUtil;

    @Mock
    private CompanySizeSegmentService companySizeSegmentService;

    @Mock
    private CompanySizeFilterService companySizeFilterService;

    @Mock
    private WaldoService waldoService;

    private MainTask mainTask;

    @BeforeEach
    public void setup() {
        mainTask = new MainTask(
                logEntryFactory,
                proctorUtil,
                advertiserServiceUtil,
                jobUrlUtil,
                docServiceUtil,
                metricsEmitter,
                clock,
                waldoService,
                commsHubService,
                companySizeSegmentService,
                companySizeFilterService);

        try {
            when(advertiserServiceUtil.getPreferredLocaleWillThrow(anyInt())).thenReturn(Locale.US);
        } catch (final Exception e) {
            // Can't be reached
        }

        try {
            lenient()
                    .when(jobUrlUtil.getJobIsLiveUsFtpSponsorCtaUrl(anyInt(), anyInt()))
                    .thenReturn("sponsorLink");
            lenient()
                    .when(jobUrlUtil.getJobIsLiveUsFtpViewUrl(anyInt(), anyInt()))
                    .thenReturn("sponsorLink");
        } catch (final Exception e) {
            // Can't be reached
        }
        lenient().when(waldoQueryConfig.getBatchSize()).thenReturn(100);
        lenient()
                .when(employerJobUtil.getEmployerJobPost(anyString(), anyInt()))
                .thenReturn(Optional.of(
                        new EmployerJobQuery.OnHostedJobPost(Collections.emptyList(), "US", DateTime.now())));
        lenient().when(proctorUtil.getProctorGroups(anyInt())).thenReturn(mockProctorGroups);
        lenient()
                .when(mockProctorGroups.isJob_is_live_new_waldo_query_testNewquery())
                .thenReturn(true);

        final Map<AdvertiserServiceProtos.PropertyName, String> advertiserProperties = new HashMap<>();
        advertiserProperties.put(
                AdvertiserServiceProtos.PropertyName.SHOW_HOSTED_JOBS,
                AdvertiserServiceProtos.ShowHostedJobs.ENABLED.name());
        when(advertiserServiceUtil.getAdvertiserProperties(anyInt(), anyList())).thenReturn(advertiserProperties);

        when(proctorUtil.getProctorGroups(anyInt(), anyString(), nullable(String.class), nullable(String.class)))
                .thenReturn(mockProctorGroups);
        when(mockProctorGroups.isDrat_jobliveemailActive()).thenReturn(true);
        lenient()
                .when(mockProctorGroups.isOwned_channels_job_is_live_commshub_migrationNew())
                .thenReturn(false);
        when(timeUtil.convertToEpoch(anyString())).thenReturn(Instant.now().toEpochMilli());
        when(timeUtil.getStartOfDayMilli(any()))
                .thenReturn(Instant.now()
                        .atZone(DEFAULT_TIME_ZONE)
                        .toLocalDate()
                        .atStartOfDay(DEFAULT_TIME_ZONE)
                        .toInstant()
                        .toEpochMilli());

        lenient().when(logEntryFactory.createLogEntry(anyString())).thenReturn(mockLogEntry);

        lenient()
                .when(commsHubService.sendEmails(any(), any()))
                .thenAnswer(new Answer<List<Pair<JobActivityInfo, String>>>() {
                    public List<Pair<JobActivityInfo, String>> answer(InvocationOnMock invocation) throws Throwable {
                        final List<Pair<JobActivityInfo, String>> responseList = new ArrayList<>();
                        responseList.add(
                                Pair.of(((List<JobActivityInfo>) invocation.getArguments()[0]).get(0), "ACK"));
                        return responseList;
                    }
                });

        // WALDO-specific mocking
        try {

            final long[] jobIds = {1924485713l};
            final HashMap<Object, AggregationQueryResponse.RefineByBucket> subSubBuckets = new HashMap<>();
            final AggregationQueryResponse.RefineByBucket subSubBucket = new AggregationQueryResponse.RefineByBucket(
                    1108, 1, 1, 1, 1, jobIds, null, null, null, new HashMap<>(), 1, null, null, null, null);
            subSubBuckets.put(1, subSubBucket);

            final HashMap<Object, AggregationQueryResponse.RefineByBucket> subBuckets = new HashMap<>();
            final HashMap<Object, AggregationQueryResponse.RefineByBucket> ftpSubBuckets = new HashMap<>();
            final AggregationQueryResponse.RefineByBucket subBucket = new AggregationQueryResponse.RefineByBucket(
                    1108, 1, 1, 1, 1, jobIds, null, null, null, subSubBuckets, 1, null, null, null, null);
            subBuckets.put(1, subBucket); // 1108 is "US" in base 36

            final AggregationQueryResponse.RefineByBucket ftpSubBucket = new AggregationQueryResponse.RefineByBucket(
                    1108, 1, 1, 1, 1, jobIds, null, null, null, subSubBuckets, 1, null, null, null, null);
            ftpSubBuckets.put(0, ftpSubBucket); // 1108 is "US" in base 36

            final ArrayList<AggregationQueryResponse.RefineByBucket> resultBuckets = new ArrayList<>();

            resultBuckets.add(new AggregationQueryResponse.RefineByBucket(
                    "1344344338-2", 1, 1, 1, 1, null, null, null, null, subBuckets, 1, null, null, null, null));

            resultBuckets.add(new AggregationQueryResponse.RefineByBucket(
                    "1344344338-3", 1, 1, 1, 1, null, null, null, null, ftpSubBuckets, 1, null, null, null, null));

            when(waldoClient.doAggregationQuery(any(AggregationQuery.class)))
                    .thenReturn(new AggregationQueryResponse(
                            1L,
                            1L,
                            resultBuckets,
                            null,
                            null,
                            1000L,
                            2L,
                            1,
                            0,
                            1,
                            null,
                            null,
                            null,
                            null,
                            Collections.emptyMap(),
                            Map.of(RefineByType.EMPLOYER_JOB_ID.toString(), Map.of(1108L, "UUID"))));
        } catch (final Exception e) {
            // Can't happen
        }
    }

    @Test
    public void GIVEN_validJobsAndjobislive_WHEN_toolRun_THEN_twoEmailsSent() throws Exception {
        // GIVEN
        when(mockProctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v1()).thenReturn(true);

        // WHEN
        mainTask.run(DEFAULT_TASK_ARGUMENTS);

        // THEN
        final ArgumentCaptor<List<JobActivityInfo>> captorPtp = ArgumentCaptor.forClass(List.class);
        final ArgumentCaptor<List<JobActivityInfo>> captorFtp = ArgumentCaptor.forClass(List.class);
        verify(commsHubService, times(1)).sendEmails(captorPtp.capture(), eq(CAMPAIGN_JOB_IS_LIVE));
        verify(commsHubService, times(1)).sendEmails(captorFtp.capture(), eq(CAMPAIGN_JOB_IS_LIVE_US_FTP));

        verify(logEntryFactory, times(2)).createLogEntry(JOB_IS_LIVE_EMAIL);
        final List<JobActivityInfo> batchPtp = captorPtp.getValue();
        final List<JobActivityInfo> batchUsFtp = captorFtp.getValue();
        // ptp
        assertEquals(1, batchPtp.size());
        assertEquals(
                batchPtp.get(0).getRavenPayload().getDataMap().get("idempotencyKey"),
                getIdempotencyKeyWithJobId(1344344338, 2));
        // ftp
        assertEquals(1, batchUsFtp.size());
        assertEquals(
                batchUsFtp.get(0).getRavenPayload().getDataMap().get("idempotencyKey"),
                getIdempotencyKeyWithJobId(1344344338, 3));

        verify(metricsEmitter).emitWaldoResultSize(2);
        verify(metricsEmitter, times(4))
                .emitExternalCallProcessTime(eq(START_TIME), eq(ExternalService.ADVERTISER_SERVICE), eq(true));
        verify(metricsEmitter, times(2))
                .emitExternalCallProcessTime(eq(START_TIME), eq(ExternalService.WALDO), eq(true));
        verify(metricsEmitter, times(2))
                .emitExternalCallProcessTime(eq(START_TIME), eq(ExternalService.COMMS_HUB), eq(true));
        verify(metricsEmitter).emitJobFinished(eq(START_TIME));
        verify(metricsEmitter, times(2)).emitWaldoRecordProcessed(any());
    }

    @Test
    public void GIVEN_validJpFtpAndPtpJobs_WHEN_toolRun_THEN_twoEmailsSent() throws Exception {
        // GIVEN
        when(mockProctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v1_JP())
                .thenReturn(true);
        // WHEN
        mainTask.run(DEFAULT_TASK_ARGUMENTS);

        // THEN
        final ArgumentCaptor<List<JobActivityInfo>> captorPtp = ArgumentCaptor.forClass(List.class);
        final ArgumentCaptor<List<JobActivityInfo>> captorFtp = ArgumentCaptor.forClass(List.class);
        verify(commsHubService, times(1)).sendEmails(captorPtp.capture(), eq(CAMPAIGN_JOB_IS_LIVE_JP_PTP));
        verify(commsHubService, times(1)).sendEmails(captorFtp.capture(), eq(CAMPAIGN_JOB_IS_LIVE_JP_FTP));

        // ptp
        final List<JobActivityInfo> batchPtp = captorPtp.getValue();
        assertEquals(1, batchPtp.size());
        assertEquals(
                batchPtp.get(0).getRavenPayload().getDataMap().get("idempotencyKey"),
                getIdempotencyKeyWithJobId(1344344338, 2));
        // ftp
        final List<JobActivityInfo> batchFtp = captorFtp.getValue();
        assertEquals(1, batchFtp.size());
        assertEquals(
                batchFtp.get(0).getRavenPayload().getDataMap().get("idempotencyKey"),
                getIdempotencyKeyWithJobId(1344344338, 3));
        verify(logEntryFactory, times(2)).createLogEntry(JOB_IS_LIVE_EMAIL);
    }

    @Test
    public void GIVEN_jpBulkUploadedJob_WHEN_toolRun_THEN_noEmailSent() throws Exception {
        // GIVEN
        when(mockProctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v1_JP())
                .thenReturn(true);
        when(employerJobUtil.getEmployerJobPost(anyString(), anyInt()))
                .thenReturn(Optional.of(new EmployerJobQuery.OnHostedJobPost(
                        List.of(new EmployerJobQuery.Attribute("bulkuploaded", "true")), "JP", DateTime.now())));

        // WHEN
        mainTask.run(DEFAULT_TASK_ARGUMENTS);

        // THEN
        verify(commsHubService, times(0)).sendEmails(any(), anyString());
        verifyNoMoreInteractions(commsHubService);
        verify(logEntryFactory, times(2)).createLogEntry(JOB_IS_LIVE_EMAIL);
    }

    @Test
    public void GIVEN_usBulkUploadedJob_WHEN_toolRun_THEN_oneEmailSent() throws Exception {
        // GIVEN
        when(mockProctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v1()).thenReturn(true);
        when(employerJobUtil.getEmployerJobPost(anyString(), anyInt()))
                .thenReturn(Optional.of(new EmployerJobQuery.OnHostedJobPost(
                        List.of(new EmployerJobQuery.Attribute("bulkuploaded", "true")), "US", DateTime.now())));

        // WHEN
        mainTask.run(DEFAULT_TASK_ARGUMENTS);

        // THEN
        final ArgumentCaptor<List<JobActivityInfo>> captor = ArgumentCaptor.forClass(List.class);
        verify(commsHubService, times(1)).sendEmails(captor.capture(), eq(CAMPAIGN_JOB_IS_LIVE));
        verify(logEntryFactory, times(2)).createLogEntry(JOB_IS_LIVE_EMAIL);
        final List<JobActivityInfo> batchUsFtp = captor.getValue();
        assertEquals(1, batchUsFtp.size());
    }

    @Test
    public void GIVEN_validJobIsLiveIntlV2_WHEN_toolRun_THEN_twoEmailsSent() throws Exception {

        // GIVEN
        when(mockProctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp())
                .thenReturn(true);

        // WHEN
        mainTask.run(DEFAULT_TASK_ARGUMENTS);

        // THEN
        final ArgumentCaptor<List<JobActivityInfo>> captor = ArgumentCaptor.forClass(List.class);
        verify(commsHubService, times(1)).sendEmails(captor.capture(), anyString());
        verify(logEntryFactory, times(1)).createLogEntry(JOB_IS_LIVE_EMAIL);
        final List<JobActivityInfo> batchUsFtp = captor.getValue();
        assertEquals(1, batchUsFtp.size());
        assertEquals(batchUsFtp.get(0).getRavenCampaign(), "jobislive_v2_intl_ftp");
    }
}
