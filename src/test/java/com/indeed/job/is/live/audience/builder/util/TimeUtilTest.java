package com.indeed.job.is.live.audience.builder.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class TimeUtilTest {
    private TimeUtil timeUtil;
    private static final ZoneId DEFAULT_TIME_ZONE = ZoneId.of("Etc/UTC");

    @BeforeEach
    public void setup() {
        timeUtil = new TimeUtil();
    }

    @Test
    public void testGetStartOfDayMilli() {
        final Instant currentDate =
                LocalDate.of(2023, 01, 31).atStartOfDay(DEFAULT_TIME_ZONE).toInstant();
        final long expectedResult = LocalDate.of(2023, 01, 31)
                .atStartOfDay(DEFAULT_TIME_ZONE)
                .toInstant()
                .toEpochMilli();

        final long dateCreatedMilli = timeUtil.getStartOfDayMilli(currentDate);
        assertEquals(expectedResult, dateCreatedMilli);
    }

    @Test
    void testConvertToEpoch() {
        // Arrange
        final String dateTime = "2023-03-04T23:43:19.000Z"; // ISO-8601 format which is using in onegraph query
        final long expectedEpochMillis = Instant.parse(dateTime).toEpochMilli();

        // Act
        final long actualEpochMillis = timeUtil.convertToEpoch(dateTime);

        // Assert
        assertEquals(expectedEpochMillis, actualEpochMillis);
    }
}
