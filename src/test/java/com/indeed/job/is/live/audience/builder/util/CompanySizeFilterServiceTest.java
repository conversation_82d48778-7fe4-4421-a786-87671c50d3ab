package com.indeed.job.is.live.audience.builder.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class CompanySizeFilterServiceTest {
    @InjectMocks
    private CompanySizeFilterService companySizeFilterService;

    @Test
    public void GIVEN_smallSize_WHEN_isCompanySizeSmall_THEN_returnsTrue() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isSmallEmployer("S");

        // THEN
        assertTrue(resp);
    }

    @Test
    public void GIVEN_smallSize_WHEN_isNotCompanySizeSmall_THEN_returnsFalse() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isSmallEmployer("M");

        // THEN
        assertFalse(resp);
    }

    @Test
    public void GIVEN_mediumSize_WHEN_isCompanySizeMedium_THEN_returnsTrue() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isMediumEmployer("M");

        // THEN
        assertTrue(resp);
    }

    @Test
    public void GIVEN_mediumSize_WHEN_isNotCompanySizeMedium_THEN_returnsFalse() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isMediumEmployer("S");

        // THEN
        assertFalse(resp);
    }

    @Test
    public void GIVEN_largerSize_WHEN_isCompanySizeLarger_THEN_returnsTrue() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isLargerEmployer("L");

        // THEN
        assertTrue(resp);
    }

    @Test
    public void GIVEN_largerSize_WHEN_isNotCompanySizeLarger_THEN_returnsFalse() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isLargerEmployer("S");

        // THEN
        assertFalse(resp);
    }

    @Test
    public void GIVEN_extraLargerSize_WHEN_isCompanySizeExtraLarger_THEN_returnsTrue() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isExtraLargerEmployer("XL");

        // THEN
        assertTrue(resp);
    }

    @Test
    public void GIVEN_extraLargerSize_WHEN_isNotCompanySizeExtraLarger_THEN_returnsFalse() {
        // GIVEN

        // WHEN
        final boolean resp = companySizeFilterService.isExtraLargerEmployer("S");

        // THEN
        assertFalse(resp);
    }
}
