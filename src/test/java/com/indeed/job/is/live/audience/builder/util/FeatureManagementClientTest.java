package com.indeed.job.is.live.audience.builder.util;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.indeed.library.featuremanagementcommon.model.ProfileValuesResult;
import com.indeed.library.featuremanagementcommon.service.FeatureManagementService;
import okhttp3.MediaType;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FeatureManagementClientTest {
    @InjectMocks
    private FeatureManagementClient featureManagementClient;

    @Mock
    private FeatureManagementService mockFeatureManagementService;

    @Mock
    private Call<ProfileValuesResult> call;

    private final int mockAdvertiserId = 1;

    @Test
    public void testGetSegmentSizeFeatureManagementServiceResponseIsXLSegmentSize() throws IOException {
        final String expectedSegmentSize = "XL";
        final Map<String, Map<String, Object>> traits =
                Maps.newHashMap(ImmutableMap.of("advertiser", Map.of("segmentSize", expectedSegmentSize)));
        final ProfileValuesResult result = new ProfileValuesResult();
        result.setTraits(traits);
        when(mockFeatureManagementService.getProfileValues(anyString(), anyLong()))
                .thenReturn(call);
        when(call.execute()).thenReturn(Response.success(result));
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(mockAdvertiserId);
        assertEquals(expectedSegmentSize, optionalSegmentSize.orElse(null));
    }

    @Test
    public void testGetSegmentSizeReturnsEmptyWhenSegmentSizeParameterDoesNotExist() throws IOException {
        final Map<String, Map<String, Object>> traits =
                Maps.newHashMap(ImmutableMap.of("advertiser", Map.of("someData", "someData")));
        final ProfileValuesResult result = new ProfileValuesResult();
        result.setTraits(traits);
        when(mockFeatureManagementService.getProfileValues(anyString(), anyLong()))
                .thenReturn(call);
        when(call.execute()).thenReturn(Response.success(result));
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(mockAdvertiserId);
        assertEquals(Optional.empty(), optionalSegmentSize);
    }

    @Test
    public void testGetSegmentSizeReturnsEmptyWhenThereAreNoTraits() throws IOException {
        final ProfileValuesResult result = new ProfileValuesResult();
        when(mockFeatureManagementService.getProfileValues(anyString(), anyLong()))
                .thenReturn(call);
        when(call.execute()).thenReturn(Response.success(result));
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(mockAdvertiserId);
        assertEquals(Optional.empty(), optionalSegmentSize);
    }

    @Test
    public void testGetSegmentSizeReturnsEmptyWhenThereAreNoResponseBody() throws IOException {
        final ProfileValuesResult result = null;
        when(mockFeatureManagementService.getProfileValues(anyString(), anyLong()))
                .thenReturn(call);
        when(call.execute()).thenReturn(Response.success(result));
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(mockAdvertiserId);
        assertEquals(Optional.empty(), optionalSegmentSize);
    }

    @Test
    public void testGetSegmentSizeReturnsEmptyWhenAdvertiserDoesNotExists() throws IOException {
        final Map<String, Map<String, Object>> traits =
                Maps.newHashMap(ImmutableMap.of("account", Map.of("someData", "someData")));
        final ProfileValuesResult result = new ProfileValuesResult();
        result.setTraits(traits);
        when(mockFeatureManagementService.getProfileValues(anyString(), anyLong()))
                .thenReturn(call);
        when(call.execute()).thenReturn(Response.success(result));
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(mockAdvertiserId);
        assertEquals(Optional.empty(), optionalSegmentSize);
    }

    @Test
    public void testGetSegmentSizeReturnsEmptyWhenResponseIsNotSuccessful() throws IOException {
        final String mockMediaType = "application/json";
        final Response<ProfileValuesResult> errorResponse =
                Response.error(404, ResponseBody.create(MediaType.get(mockMediaType), "Not Found"));
        when(mockFeatureManagementService.getProfileValues(anyString(), anyLong()))
                .thenReturn(call);
        when(call.execute()).thenReturn(errorResponse);
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(mockAdvertiserId);
        assertEquals(Optional.empty(), optionalSegmentSize);
    }
}
