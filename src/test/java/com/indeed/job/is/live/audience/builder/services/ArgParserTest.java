package com.indeed.job.is.live.audience.builder.services;

import com.google.common.collect.ImmutableSet;
import com.indeed.job.is.live.audience.builder.models.TaskArguments;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.DefaultApplicationArguments;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class ArgParserTest {
    private static final String DEFAULT_JIL_LOOKBACK_DAYS = "--jobIsLiveLookbackDays=7";
    private static final String DEFAULT_TEST_HQM_ADVERTISER_IDS = "--testHqmAdvertiserIds=123,456";
    private static final String DEFAULT_HQM_QA_TEST_RUN = "--hqmQaTestRun=1";
    private static final String DEFAULT_ADH_TEST_RUN = "--adhTestRun=1";
    private static final String DEFAULT_FILTER_WALDO_EMAILS_ALREADY_SENT = "--filterWaldoEmailsAlreadySent=1";

    @InjectMocks
    private ArgParser argParser;

    @Test
    public void GIVEN_allArgs_WHEN_parse_THEN_returnsTaskArguments() {
        // GIVEN
        final ApplicationArguments args = new DefaultApplicationArguments(
                DEFAULT_JIL_LOOKBACK_DAYS,
                DEFAULT_TEST_HQM_ADVERTISER_IDS,
                DEFAULT_HQM_QA_TEST_RUN,
                DEFAULT_ADH_TEST_RUN,
                DEFAULT_FILTER_WALDO_EMAILS_ALREADY_SENT);
        final TaskArguments expectedArgs = TaskArguments.builder()
                .jobIsLiveLookbackDays(7)
                .testHqmAdvertiserIds(ImmutableSet.of(123, 456))
                .hqmQaTestRun(true)
                .adhTestRun(true)
                .filterWaldoEmailsAlreadySent(true)
                .build();

        // WHEN
        final TaskArguments parsedArgs = argParser.parse(args);

        // THEN
        assertEquals(expectedArgs, parsedArgs);
    }

    @Test
    public void GIVEN_noArgs_WHEN_parse_THEN_returnsTaskArgumentsWithDefaultValues() {
        // GIVEN
        final ApplicationArguments args = new DefaultApplicationArguments();
        final TaskArguments expectedArgs = TaskArguments.builder()
                .jobIsLiveLookbackDays(7)
                .testHqmAdvertiserIds(ImmutableSet.of())
                .hqmQaTestRun(false)
                .adhTestRun(false)
                .filterWaldoEmailsAlreadySent(true)
                .build();

        // WHEN
        final TaskArguments parsedArgs = argParser.parse(args);

        // THEN
        assertEquals(expectedArgs, parsedArgs);
    }
}
