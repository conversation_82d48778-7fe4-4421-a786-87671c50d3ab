package com.indeed.job.is.live.audience.builder.util;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableMap;
import com.indeed.docservice.DocServiceProtos;
import com.indeed.docservice.client.BoxcarDocServiceRpcClient;
import com.indeed.job.is.live.audience.builder.models.JobActivityInfo;
import com.indeed.job.is.live.audience.builder.services.MainTask;
import com.indeed.job.is.live.audience.builder.services.MainTask.DradisJobId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DocServiceUtilTest {
    private static final int ADVERTISER_ID = 1;
    private static final long AGG_JOB_ID = 1L;

    private static final int ATS_JOB_ID = 2;
    private static final JobActivityInfo JOB_ACTIVITY_INFO = JobActivityInfo.builder()
            .advertiserID(ADVERTISER_ID)
            .atsJobID(ATS_JOB_ID)
            .campaignType("campaign_type")
            .dateCreated(1L)
            .country("country")
            .aggJobID(AGG_JOB_ID)
            .build();
    private static final DradisJobId DRADIS_JOB_ID = new MainTask.DradisJobId(ADVERTISER_ID, ATS_JOB_ID);

    private static final Map<DradisJobId, JobActivityInfo> JOBS = ImmutableMap.of(DRADIS_JOB_ID, JOB_ACTIVITY_INFO);

    @Mock
    private BoxcarDocServiceRpcClient mockBoxcarDocServiceRpcClient;

    @Mock
    private AnnotationConfigApplicationContext mockSpringContext;

    @InjectMocks
    private DocServiceUtil docServiceUtil;

    @BeforeEach
    public void setup() throws Exception {
        lenient().when(mockBoxcarDocServiceRpcClient.service(any(), any())).thenReturn(singleDocResponse(AGG_JOB_ID));
    }

    @Test
    public void GIVEN_emptyJobs_WHEN_setJobInfoForNoBillingEmail_THEN_doesNotCallBoxcarDocServiceRpcClient()
            throws Exception {
        // GIVEN
        final Map<DradisJobId, JobActivityInfo> emptyJobs = ImmutableMap.of();

        // WHEN
        docServiceUtil.setJobInfoForJobisLiveEmail(emptyJobs);

        // THEN
        verify(mockBoxcarDocServiceRpcClient, never()).service(any(), any());
    }

    @Test
    public void GIVEN_jobsMissingInDocStore_WHEN_setJobInfoForJobisLiveEmail_THEN_returrnsEmptyMap() throws Exception {
        // GIVEN
        final DocServiceProtos.DocResponse mockDoceResponse =
                DocServiceProtos.DocResponse.newBuilder().build();
        given(mockBoxcarDocServiceRpcClient.service(any(), any())).willReturn(mockDoceResponse);

        // WHEN
        final ImmutableListMultimap<DradisJobId, JobActivityInfo> result =
                docServiceUtil.setJobInfoForJobisLiveEmail(JOBS);

        // THEN
        assertTrue(result.isEmpty());
    }

    @Test
    public void GIVEN_jobs_WHEN_setJobInfoForJobisLiveEmail_THEN_callsDocService() throws Exception {
        // GIVEN
        final List<Long> jobIds = ImmutableList.of(AGG_JOB_ID);
        final DocServiceProtos.DocRequest expectedDocRequest = DocServiceProtos.DocRequest.newBuilder()
                .addAllOrganicResultsJobIds(jobIds)
                .build();

        // WHEN
        docServiceUtil.setJobInfoForJobisLiveEmail(JOBS);

        // THEN
        verify(mockBoxcarDocServiceRpcClient).service(expectedDocRequest, null);
    }

    @Test
    public void GIVEN_notResolvedDocJob_WHEN_setJobInfo_THEN_returrnsEmptyMap() throws Exception {
        // GIVEN
        final long otherDocStoreJobId = 2L;

        final DocServiceProtos.Job docJob =
                DocServiceProtos.Job.newBuilder().setId(otherDocStoreJobId).build();
        final DocServiceProtos.JobResult jobResult =
                DocServiceProtos.JobResult.newBuilder().setJob(docJob).build();
        final DocServiceProtos.DocResponse mockDocResponse = DocServiceProtos.DocResponse.newBuilder()
                .addOrganicResults(jobResult)
                .build();
        given(mockBoxcarDocServiceRpcClient.service(any(), any())).willReturn(mockDocResponse);

        // WHEN
        final ImmutableListMultimap<DradisJobId, JobActivityInfo> result =
                docServiceUtil.setJobInfoForJobisLiveEmail(JOBS);

        // THEN
        assertTrue(result.isEmpty());
    }

    @Test
    public void GIVEN_resolvedDocJobWithoutZipCode_WHEN_setJobInfoForJobisLiveEmail_THEN_setsFieldsFromDocService()
            throws Exception {
        // GIVEN
        final String jobTitle = "job title";
        final String jobCity = "job city";
        final String jobState = "job state";
        final String jobLocation = jobCity + "," + jobState;
        final String company = "company";

        final DocServiceProtos.Job docJob = DocServiceProtos.Job.newBuilder()
                .setId(AGG_JOB_ID)
                .setMetadataAdvertiserId(ADVERTISER_ID)
                .setCity(jobCity)
                .setState(jobState)
                .setTitle(jobTitle)
                .setCompany(company)
                .build();

        final DocServiceProtos.JobResult jobResult =
                DocServiceProtos.JobResult.newBuilder().setJob(docJob).build();
        final DocServiceProtos.DocResponse mockResponse = DocServiceProtos.DocResponse.newBuilder()
                .addOrganicResults(jobResult)
                .build();
        given(mockBoxcarDocServiceRpcClient.service(any(), any())).willReturn(mockResponse);

        final JobActivityInfo jobActivityInfo = JOB_ACTIVITY_INFO;
        jobActivityInfo.setLocation(jobLocation);
        jobActivityInfo.setTitle(jobTitle);
        jobActivityInfo.setCompany(company);

        final ImmutableListMultimap<DradisJobId, JobActivityInfo> expectedResult =
                ImmutableListMultimap.of(DRADIS_JOB_ID, jobActivityInfo);

        // WHEN
        final ImmutableListMultimap<DradisJobId, JobActivityInfo> result =
                docServiceUtil.setJobInfoForJobisLiveEmail(JOBS);

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    public void GIVEN_resolvedDocJobWithEmptyZipCode_WHEN_setJobInfoForJobisLiveEmail_THEN_setsFieldsFromDocService()
            throws Exception {
        // GIVEN
        final String jobTitle = "job title";
        final String jobCity = "job city";
        final String jobState = "job state";
        final String jobLocation = jobCity + "," + jobState;
        final String company = "company";

        final DocServiceProtos.Job docJob = DocServiceProtos.Job.newBuilder()
                .setId(AGG_JOB_ID)
                .setMetadataAdvertiserId(ADVERTISER_ID)
                .setCity(jobCity)
                .setState(jobState)
                .setTitle(jobTitle)
                .setCompany(company)
                .setZip("")
                .build();

        final DocServiceProtos.JobResult jobResult =
                DocServiceProtos.JobResult.newBuilder().setJob(docJob).build();
        final DocServiceProtos.DocResponse mockResponse = DocServiceProtos.DocResponse.newBuilder()
                .addOrganicResults(jobResult)
                .build();
        given(mockBoxcarDocServiceRpcClient.service(any(), any())).willReturn(mockResponse);

        final JobActivityInfo jobActivityInfo = JOB_ACTIVITY_INFO;
        jobActivityInfo.setLocation(jobLocation);
        jobActivityInfo.setTitle(jobTitle);
        jobActivityInfo.setCompany(company);

        final ImmutableListMultimap<DradisJobId, JobActivityInfo> expectedResult =
                ImmutableListMultimap.of(DRADIS_JOB_ID, jobActivityInfo);

        // WHEN
        final ImmutableListMultimap<DradisJobId, JobActivityInfo> result =
                docServiceUtil.setJobInfoForJobisLiveEmail(JOBS);

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    public void GIVEN_resolvedDocJobWithZipCode_WHEN_setJobInfoForJobisLiveEmail_THEN_setsFieldsFromDocService()
            throws Exception {
        // GIVEN
        final String jobTitle = "job title";
        final String jobCity = "job city";
        final String jobState = "job state";
        final String company = "company";
        final String zipCode = "1234";
        final String jobLocation = jobCity + "," + jobState + zipCode;

        final DocServiceProtos.Job docJob = DocServiceProtos.Job.newBuilder()
                .setId(AGG_JOB_ID)
                .setMetadataAdvertiserId(ADVERTISER_ID)
                .setCity(jobCity)
                .setState(jobState)
                .setTitle(jobTitle)
                .setCompany(company)
                .setZip(zipCode)
                .build();

        final DocServiceProtos.JobResult jobResult =
                DocServiceProtos.JobResult.newBuilder().setJob(docJob).build();
        final DocServiceProtos.DocResponse mockResponse = DocServiceProtos.DocResponse.newBuilder()
                .addOrganicResults(jobResult)
                .build();
        given(mockBoxcarDocServiceRpcClient.service(any(), any())).willReturn(mockResponse);

        final JobActivityInfo jobActivityInfo = JOB_ACTIVITY_INFO;
        jobActivityInfo.setLocation(jobLocation);
        jobActivityInfo.setTitle(jobTitle);
        jobActivityInfo.setCompany(company);

        final ImmutableListMultimap<DradisJobId, JobActivityInfo> expectedResult =
                ImmutableListMultimap.of(DRADIS_JOB_ID, jobActivityInfo);

        // WHEN
        final ImmutableListMultimap<DradisJobId, JobActivityInfo> result =
                docServiceUtil.setJobInfoForJobisLiveEmail(JOBS);

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    public void GIVEN_moreThan50Jobs_WHEN_setJobInfoForJobisLiveEmail_THEN_batchesCallsToDocService() throws Exception {
        // GIVEN
        final Map<DradisJobId, JobActivityInfo> jobs = new HashMap<>();
        for (int i = 1; i <= 76; i++) {
            jobs.put(
                    new DradisJobId(i, i),
                    JobActivityInfo.builder()
                            .advertiserID(1)
                            .atsJobID(1)
                            .campaignType("campaign")
                            .dateCreated(1L)
                            .country("country")
                            .aggJobID(i)
                            .build());
        }

        final int expectedNumCalls = 2; // batch size is 50, so called twice for 75 jobs
        when(mockBoxcarDocServiceRpcClient.service(any(), any()))
                .thenReturn(getDocResponse(1, 50), getDocResponse(51, 75));

        // WHEN
        docServiceUtil.setJobInfoForJobisLiveEmail(jobs);

        // THEN
        verify(mockBoxcarDocServiceRpcClient, times(expectedNumCalls)).service(any(), any());
    }

    private DocServiceProtos.DocResponse singleDocResponse(final long jobId) {
        return DocServiceProtos.DocResponse.newBuilder()
                .addOrganicResults(DocServiceProtos.JobResult.newBuilder()
                        .setJob(DocServiceProtos.Job.newBuilder()
                                .setId(jobId)
                                .setMetadataAdvertiserId(ADVERTISER_ID)
                                .build())
                        .build())
                .build();
    }

    private DocServiceProtos.DocResponse getDocResponse(final long startId, final long endId) {
        final DocServiceProtos.DocResponse.Builder responseBuilder = DocServiceProtos.DocResponse.newBuilder();
        for (long i = startId; i <= endId; i++) {
            responseBuilder.addOrganicResults(DocServiceProtos.JobResult.newBuilder()
                    .setJob(DocServiceProtos.Job.newBuilder()
                            .setId(i)
                            .setMetadataAdvertiserId(ADVERTISER_ID)
                            .build())
                    .build());
        }
        return responseBuilder.build();
    }
}
