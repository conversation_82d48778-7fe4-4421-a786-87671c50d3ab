package com.indeed.job.is.live.audience.builder.config;

import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.enums.JobIsLiveFinalState;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;

import static com.indeed.job.is.live.audience.builder.enums.MetricNames.METRIC_EXTERNAL_CALL_PROCESS_TIME;
import static com.indeed.job.is.live.audience.builder.enums.MetricNames.METRIC_JOB_FINISHED;
import static com.indeed.job.is.live.audience.builder.enums.MetricNames.METRIC_TASK_BATCH_SIZE;
import static com.indeed.job.is.live.audience.builder.enums.MetricNames.METRIC_TASK_RECORD_PROCESSED;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_BATCH_SIZE;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_CALL_SUCCESS;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_EXTERNAL_SERVICE;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_JOB_SUCCESS;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_RECORD_FINAL_STATE;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MetricsEmitterTest {

    @Mock
    private MeterRegistry meterRegistry;

    private final Clock fixedClock = Clock.fixed(Instant.now(), ZoneId.systemDefault());

    @Mock
    private Timer timer;

    @Mock
    private Counter counter;

    private MetricsEmitter metricsEmitter;

    @BeforeEach
    public void setup() {
        metricsEmitter = new MetricsEmitter(meterRegistry, fixedClock);
    }

    @Test
    public void testEmitWaldoResultSize() {
        when(meterRegistry.counter(anyString(), anyList())).thenReturn(counter);

        metricsEmitter.emitWaldoResultSize(10);

        final Iterable<Tag> expectedTags = List.of(Tag.of(TAG_BATCH_SIZE, "10"));

        verify(meterRegistry).counter(eq(METRIC_TASK_BATCH_SIZE), eq(expectedTags));
        verify(counter).increment();

        verifyNoMoreInteractions(meterRegistry);
        verifyNoMoreInteractions(counter);
    }

    @Test
    public void testEmitWaldoRecordProcessed() {
        when(meterRegistry.counter(anyString(), anyList())).thenReturn(counter);

        metricsEmitter.emitWaldoRecordProcessed(JobIsLiveFinalState.FILTERED);

        final List<Tag> expectedTags = List.of(Tag.of(TAG_RECORD_FINAL_STATE, JobIsLiveFinalState.FILTERED.name()));

        verify(meterRegistry).counter(eq(METRIC_TASK_RECORD_PROCESSED), eq(expectedTags));
        verify(counter).increment();

        verifyNoMoreInteractions(meterRegistry);
        verifyNoMoreInteractions(counter);
    }

    @Test
    public void testEmitJobFinished() {
        when(meterRegistry.timer(anyString(), anyList())).thenReturn(timer);

        metricsEmitter.emitJobFinished(fixedClock.millis());

        final List<Tag> expectedTags = List.of(Tag.of(TAG_JOB_SUCCESS, Boolean.toString(Boolean.TRUE)));

        verify(meterRegistry).timer(eq(METRIC_JOB_FINISHED), eq(expectedTags));

        verifyNoMoreInteractions(meterRegistry);
        verifyNoMoreInteractions(counter);
    }

    @Test
    public void testEmitExternalCallProcessTime() {
        when(meterRegistry.timer(anyString(), anyList())).thenReturn(timer);

        final ExternalService externalService = ExternalService.WALDO;
        final boolean callSuccess = true;

        metricsEmitter.emitExternalCallProcessTime(fixedClock.millis(), externalService, callSuccess);

        final List<Tag> expectedTags = List.of(
                Tag.of(TAG_EXTERNAL_SERVICE, externalService.name()),
                Tag.of(TAG_CALL_SUCCESS, Boolean.toString(callSuccess)));

        verify(meterRegistry).timer(eq(METRIC_EXTERNAL_CALL_PROCESS_TIME), eq(expectedTags));

        verifyNoMoreInteractions(meterRegistry);
        verifyNoMoreInteractions(counter);
    }
}
