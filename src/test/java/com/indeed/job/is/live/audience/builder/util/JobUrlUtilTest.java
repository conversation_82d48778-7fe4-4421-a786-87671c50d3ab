package com.indeed.job.is.live.audience.builder.util;

import com.indeed.job.is.live.audience.builder.config.JobIsLiveAudienceBuilderPropertiesConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.indeed.job.is.live.audience.builder.util.EmployerJobUtil.atsJobIdToLegacyJobId;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class JobUrlUtilTest {

    @Mock
    private JobIsLiveAudienceBuilderPropertiesConfig jobIsLiveAudienceBuilderPropertiesConfig;

    private JobUrlUtil jobUrlUtil;

    @BeforeEach
    public void setup() {
        jobUrlUtil = new JobUrlUtil(jobIsLiveAudienceBuilderPropertiesConfig);
    }

    @Test
    void getJobIsLiveUsFtpSponsorCtaUrl() {
        final String employerBaseUrl = "https://example.com";
        when(jobIsLiveAudienceBuilderPropertiesConfig.getEmployerBaseUrl()).thenReturn(employerBaseUrl);

        final int advertiserId = 123;
        final int atsJobId = 456;
        final String legacyJobId = atsJobIdToLegacyJobId(atsJobId, advertiserId);

        final String sponsorJobLink = jobUrlUtil.getJobIsLiveUsFtpSponsorCtaUrl(advertiserId, atsJobId);

        final String expectedLink = employerBaseUrl + "/sponsor/edit" + "?publishedId=" + legacyJobId
                + "&sid=us_tmp_uc_always-on_smbx_ch_email";

        assertEquals(expectedLink, sponsorJobLink);
    }

    @Test
    void getJobIsLiveUsFtpViewUrl() {
        final String employerBaseUrl = "https://example.com";
        when(jobIsLiveAudienceBuilderPropertiesConfig.getEmployerBaseUrl()).thenReturn(employerBaseUrl);

        final int advertiserId = 123;
        final int atsJobId = 456;
        final String legacyJobId = atsJobIdToLegacyJobId(atsJobId, advertiserId);

        final String sponsorJobLink = jobUrlUtil.getJobIsLiveUsFtpViewUrl(advertiserId, atsJobId);

        final String expectedLink =
                employerBaseUrl + "/jobs/view" + "?id=" + legacyJobId + "&sid=us_tmp_uc_always-on_smbx_ch_email";

        assertEquals(expectedLink, sponsorJobLink);
    }
}
