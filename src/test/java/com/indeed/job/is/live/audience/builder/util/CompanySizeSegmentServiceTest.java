package com.indeed.job.is.live.audience.builder.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CompanySizeSegmentServiceTest {
    private static final int ADV_ID = 1;

    @Mock
    private FeatureManagementClient mockFeatureManagementClient;

    @Test
    void GIVEN_advertiser_WHEN_hasSegmentSize_THEN_returnResult() throws Exception {
        CompanySizeSegmentService companySizeSegmentService =
                new CompanySizeSegmentService(mockFeatureManagementClient);

        when(mockFeatureManagementClient.getSegmentSize(ADV_ID)).thenReturn(Optional.of("M"));

        String result = companySizeSegmentService.getCompanySizeSegment(ADV_ID);

        assertEquals("M", result);
    }

    @Test
    void GIVEN_advertiser_WHEN_notHasSegmentSize_THEN_returnS() throws Exception {
        CompanySizeSegmentService companySizeSegmentService =
                new CompanySizeSegmentService(mockFeatureManagementClient);

        when(mockFeatureManagementClient.getSegmentSize(ADV_ID)).thenReturn(Optional.empty());

        String result = companySizeSegmentService.getCompanySizeSegment(ADV_ID);

        assertEquals("S", result);
    }
}
