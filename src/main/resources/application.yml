# bongo config. Not needed if you aren't connecting to any boxcar services.
bongo.config.dir: ${indeed.base}/conf

# rmq properties file
adcentral.rabbit.config.file: ${indeed.base}/conf/adc-events-dradis-api-aws-producer-rabbit-config.properties

# OneGraph
one-graph.base-url: http://${MESH_HOST_AND_PORT:localhost:${PORT4:4444}}/one-graph-batch

employer:
  base:
    url: https://employers.qa.indeed.net
base:
  dradis:
    url: https://employers.qa.indeed.net/
  iri:
    context: apis.qa.indeed.net
  view:
    job:
      url: https://employers.qa.indeed.net/j/view-job/

waldo:
  query:
    batchSize: 100
    jobAgeInMinutes: 60
---

spring:
  config:
    activate:
      on-profile: env-local

---

spring:
  config:
    activate:
      on-profile: env-qa
employer:
  base:
    url: https://employers.qa.indeed.net
base:
  dradis:
    url: https://employers.qa.indeed.net/
  iri:
    context: apis.qa.indeed.net
  view:
    job:
      url: https://employers.qa.indeed.net/j/view-job/

---

spring:
  config:
    activate:
      on-profile: env-prod
employer:
  base:
    url: https://employers.indeed.com
base:
  dradis:
    url: https://employers.indeed.com/
  iri:
    context: apis.indeed.net
  view:
    job:
      url: https://employers.indeed.com/j/view-job/
