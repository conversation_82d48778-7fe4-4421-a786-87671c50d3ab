package com.indeed.job.is.live.audience.builder.config;

import com.indeed.common.base.IndeedStagingLevel;
import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.docservice.client.BoxcarDocServiceClientFactory;
import com.indeed.docservice.client.BoxcarDocServiceRpcClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DocServiceConfig {
    @Bean
    public BoxcarDocServiceRpcClient boxcarDocServiceClient() {
        final BoxcarDocServiceClientFactory factory = new BoxcarDocServiceClientFactory();

        if (IndeedStagingLevel.get() != IndeedStagingLevel.PRODUCTION) {
            factory.setBongoPool("e2eqa");
        }
        factory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        return factory.getClient();
    }
}
