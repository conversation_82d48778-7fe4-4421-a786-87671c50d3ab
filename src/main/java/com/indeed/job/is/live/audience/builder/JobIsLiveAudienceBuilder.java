package com.indeed.job.is.live.audience.builder;

import com.indeed.common.boot.config.HealthcheckConfig;
import com.indeed.job.is.live.audience.builder.models.TaskArguments;
import com.indeed.job.is.live.audience.builder.services.ArgParser;
import com.indeed.job.is.live.audience.builder.services.MainTask;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;

@SpringBootApplication(exclude = {HealthcheckConfig.class, MongoAutoConfiguration.class})
@AllArgsConstructor
@Slf4j
public class JobIsLiveAudienceBuilder implements ApplicationRunner {
    private final ArgParser argParser;

    private MainTask mainTask;

    public static void main(final String[] args) {
        new SpringApplicationBuilder(JobIsLiveAudienceBuilder.class)
                .web(WebApplicationType.NONE)
                .run(args)
                .close();
    }

    @Override
    public void run(final ApplicationArguments args) {
        final TaskArguments taskArguments = argParser.parse(args);

        try {
            mainTask.run(taskArguments);
        } catch (Exception e) {
            log.error("Error running JobIsLiveAudienceBuilder", e);
        }

        log.info("Completed JobIsLiveAudienceBuilder");
        System.exit(0); // the rad artifact continues to load, adding to end task
    }
}
