package com.indeed.job.is.live.audience.builder.config;

import com.google.common.base.Preconditions;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Nonnull;
import java.io.File;

@Configuration
public class JobIsLiveAudienceBuilderPropertiesConfig implements InitializingBean {

    private static final String DEFAULT_BONGO_DIR = "${indeed.base}/conf";

    @Value("${bongo.config.dir:" + DEFAULT_BONGO_DIR + "}")
    private File bongoConfigDirectory;

    @Value("${employer.base.url}")
    private String employerBaseUrl;

    @Value("${waldo.url:}")
    private String waldoUrl;

    @Value("${waldo.api.key:}")
    private String waldoApiKey;

    @Override
    public void afterPropertiesSet() throws Exception {
        Preconditions.checkNotNull(bongoConfigDirectory, "bongo.config.dir property not found");
    }

    @Nonnull
    public File getBongoConfigDirectory() {
        return bongoConfigDirectory;
    }

    public String getEmployerBaseUrl() {
        return employerBaseUrl;
    }

    public String getWaldoUrl() {
        return waldoUrl;
    }

    public String getWaldoApiKey() {
        return waldoApiKey;
    }
}
