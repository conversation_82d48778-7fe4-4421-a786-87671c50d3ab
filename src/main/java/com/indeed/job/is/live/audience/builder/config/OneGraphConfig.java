package com.indeed.job.is.live.audience.builder.config;

import com.apollographql.apollo3.ApolloClient;
import com.apollographql.apollo3.network.http.DefaultHttpEngine;
import com.indeed.job.is.live.audience.builder.util.OneGraphHttpClient;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
public class OneGraphConfig {
    public static final int ONE_GRAPH_TIMEOUT = 5000;

    @Bean
    @Primary
    public ApolloClient oneGraphApolloClientBuilder(
            @Value("${indeed.graphql.client.onegraph.url}") final String baseUrl,
            final OneGraphHttpClient oneGraphHttpClient) {
        return new ApolloClient.Builder()
                .httpEngine(new DefaultHttpEngine(oneGraphHttpClient.getHttpClient()))
                .serverUrl(baseUrl + "/graphql")
                .build();
    }

    @Bean
    public ExecutorService oneGraphApolloExecutor() {
        return Executors.newCachedThreadPool(new CustomizableThreadFactory("OneGraph Apollo Dispatcher #"));
    }

    @Bean
    public OkHttpClient.Builder okHttpClientBuilder() {
        return new OkHttpClient.Builder();
    }

    @Bean(destroyMethod = "dispose")
    @Primary
    public OneGraphHttpClient oneGraphHttpClientBuilder(
            @Qualifier("oneGraphApolloExecutor") final ExecutorService apolloExecutor) {
        return new OneGraphHttpClient(new Dispatcher(apolloExecutor));
    }
}
