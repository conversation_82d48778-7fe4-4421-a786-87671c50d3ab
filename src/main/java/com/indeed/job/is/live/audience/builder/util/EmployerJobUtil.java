package com.indeed.job.is.live.audience.builder.util;

import com.apollographql.apollo3.ApolloClient;
import com.apollographql.apollo3.api.ApolloResponse;
import com.apollographql.apollo3.rx3.Rx3Apollo;
import com.google.common.collect.ImmutableList;
import com.indeed.common.identification.IRI;
import com.indeed.dradis.common.encryption.EncryptionUtil;
import com.indeed.job.is.live.audience.builder.config.MetricsEmitter;
import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.exceptions.OneGraphClientException;
import com.indeed.one.graph.client.EmployerJobQuery;
import io.reactivex.rxjava3.schedulers.Schedulers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Clock;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Service
@Slf4j
public class EmployerJobUtil {
    private static final String ONEGRAPH_ADVID_HEADER_NAME = "indeed-advertiser-id";
    private static final String EMPLOYER_JOB = "EmployerJob";
    private final ApolloClient apolloClient;
    private final String iriContext;
    private final MetricsEmitter metricsEmitter;
    private final Clock clock;

    public EmployerJobUtil(
            final ApolloClient apolloClient,
            @Value("${base.iri.context}") final String iriContext,
            final MetricsEmitter metricsEmitter,
            final Clock clock) {
        this.apolloClient = apolloClient;
        this.iriContext = iriContext;
        this.metricsEmitter = metricsEmitter;
        this.clock = clock;
    }

    public Optional<EmployerJobQuery.OnHostedJobPost> getEmployerJobPost(final String uuid, final int advertiserId) {
        final IRI iri = new IRI(iriContext, EMPLOYER_JOB, uuid);
        final EmployerJobQuery nodesQuery =
                EmployerJobQuery.builder().ids(ImmutableList.of(iri.toString())).build();
        final long startTime = clock.millis();
        final CompletableFuture<EmployerJobQuery.OnHostedJobPost> future =
                new CompletableFuture<EmployerJobQuery.OnHostedJobPost>();

        Rx3Apollo.single(apolloClient
                        .query(nodesQuery)
                        .addHttpHeader(ONEGRAPH_ADVID_HEADER_NAME, String.valueOf(advertiserId)))
                .subscribeOn(Schedulers.io())
                .subscribe(response -> handleJobQueryResponse(startTime, response, uuid, future), throwable -> {
                    metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.APOLLO, false);
                    future.completeExceptionally(throwable);
                    log.error("Failure querying nodes", throwable);
                });

        log.info("Start query.enqueue nodes for advertiserId:[{}] uuid:[{}]", advertiserId, uuid);

        try {
            return Optional.of(future.get(20000, TimeUnit.MILLISECONDS));
        } catch (final ExecutionException | TimeoutException exception) {
            log.warn(exception + exception.getMessage() + exception.getCause());
            log.warn("Failed to get node for advertiser_id={} uuid={}" + exception.getMessage(), advertiserId, uuid);
            return Optional.empty();
        } catch (final InterruptedException exception) {
            Thread.currentThread().interrupt();
            log.warn("Interrupted getting node for advertiser_id={} uuid={}", advertiserId, uuid, exception);
            return Optional.empty();
        }
    }

    private void handleJobQueryResponse(
            final long startTime,
            final ApolloResponse<EmployerJobQuery.Data> response,
            final String uuid,
            final CompletableFuture<EmployerJobQuery.OnHostedJobPost> future) {
        if (response.hasErrors()) {
            future.completeExceptionally(
                    new OneGraphClientException(response.errors.get(0).getMessage()));
            metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.APOLLO, false);
            return;
        }

        try {
            metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.APOLLO, true);
            log.info("Start getting employer jobs from nodes query");
            final List<EmployerJobQuery.OnHostedJobPost> jobPosts = response.data.nodes.stream()
                    .map(r -> r.onEmployerJob.jobData.onHostedJobPost)
                    .toList();

            log.info("Finished getting employer job");

            if (jobPosts.size() != 1) {
                future.completeExceptionally(new OneGraphClientException(
                        String.format("Expected 1 node but received %s for uuid: %s", jobPosts.size(), uuid)));
                log.debug("Expected 1 node but received {} for uuid: {}", jobPosts.size(), uuid);
                return;
            }

            future.complete(jobPosts.get(0));
        } catch (final NullPointerException e) {
            future.completeExceptionally(
                    new OneGraphClientException(String.format("No node returned for uuid: %s", uuid), e));
            log.debug("No node returned for uuid: {}", uuid, e);
        } catch (final Exception e) {
            future.completeExceptionally(new OneGraphClientException(e));
            log.error("Exception querying nodes", e);
        }
    }

    public static String atsJobIdToLegacyJobId(final int atsJobId, final int advertiserId) {
        // atsJobId to legacyJobId: https://link.indeed.tech/GLYETEN2H
        return EncryptionUtil.safeEncode(atsJobId, advertiserId, EncryptionUtil.EncryptionType.JOB_ID);
    }
}
