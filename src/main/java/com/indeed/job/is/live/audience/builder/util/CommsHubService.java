package com.indeed.job.is.live.audience.builder.util;

import com.indeed.job.is.live.audience.builder.config.MetricsEmitter;
import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.models.JobActivityInfo;
import com.indeed.job.is.live.audience.builder.models.RavenClientPayload;
import com.indeed.library.communicationshubapigateway.CommunicationsHubApiGatewayProtos;
import com.indeed.library.communicationshubapigateway.CommunicationsHubApiGatewayRpcClient;
import com.indeed.util.core.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.indeed.raven.delivery.common.serialization.Serialization.objectToJsonString;

@Service
@Slf4j
public class CommsHubService {
    private final CommunicationsHubApiGatewayRpcClient commsHubApiGatewayRpcClient;
    private final MetricsEmitter metricsEmitter;
    private static final String ENC_CLIENT_TK = "JobIsLiveAudienceBuilder";

    public CommsHubService(
            final CommunicationsHubApiGatewayRpcClient commsHubApiGatewayRpcClient,
            final MetricsEmitter metricsEmitter) {
        this.commsHubApiGatewayRpcClient = commsHubApiGatewayRpcClient;
        this.metricsEmitter = metricsEmitter;
    }

    public List<Pair<JobActivityInfo, String>> sendEmails(final List<JobActivityInfo> batch, final String campaign) {
        int failures = 0;
        final List<Pair<JobActivityInfo, String>> responseList = new ArrayList<>();
        if (batch.size() > 0) {
            for (final JobActivityInfo payload : batch) {
                try {
                    final Pair<CommunicationsHubApiGatewayProtos.TemplateNotificationResponse, Boolean> result =
                            sendEmail(payload.getRavenPayload(), campaign);
                    if (!result.getSecond().booleanValue()) {
                        failures++;
                    }
                    if (result.getFirst() != null) {
                        final CommunicationsHubApiGatewayProtos.ChannelResult channelResponse =
                                result.getFirst().getChannelResult(0);
                        responseList.add(Pair.of(
                                payload, channelResponse.getResponseCode().toString()));
                    } else {
                        responseList.add(Pair.of(payload, null));
                    }
                } catch (Exception e) {
                    log.error("Failed to send email.", e);
                    responseList.add(Pair.of(payload, null));
                }
            }
            log.info("processed DRADIS_JOBISLIVE email using commshub for campaign " + campaign + " for batch with "
                    + batch.size() + " advertisers with " + failures + " not sent");
        }
        metricsEmitter.emitEmailSent((batch.size() - failures), ExternalService.COMMS_HUB, campaign, true);
        metricsEmitter.emitEmailSent(failures, ExternalService.COMMS_HUB, campaign, false);

        return responseList;
    }

    private Pair<CommunicationsHubApiGatewayProtos.TemplateNotificationResponse, Boolean> sendEmail(
            final RavenClientPayload emailPayload, final String campaign) throws Exception {
        final long advertiserId = emailPayload.getAdvertiserId();
        final Map<String, Object> dataMap = emailPayload.getDataMap();
        log.info("Sending email to advertiser {}, campaign {}", advertiserId, campaign);

        final CommunicationsHubApiGatewayProtos.SendTemplateNotificationsRequest sendTemplateNotificationsRequest =
                toSendNotificationRequest(emailPayload, dataMap, campaign);
        final CommunicationsHubApiGatewayProtos.SendTemplateNotificationsResponse sendTemplateNotificationsResponse =
                commsHubApiGatewayRpcClient.sendTemplateNotifications(sendTemplateNotificationsRequest);

        if (sendTemplateNotificationsResponse == null) {
            log.info("Failed to send email to advertiser {}, campaign {}", advertiserId, campaign);
            return Pair.of(null, false);
        }

        final CommunicationsHubApiGatewayProtos.TemplateNotificationResponse resp =
                sendTemplateNotificationsResponse.getResponsesList().get(0);
        if (!isSuccess(resp)) {
            final String errorMsg = formatResponseMsg(resp);
            log.info(
                    "Failed to send email to advertiser {}, campaign {}, errorMsg {}",
                    advertiserId,
                    campaign,
                    errorMsg);
            return Pair.of(resp, false);
        } else if (isDuplicated(resp)) {
            final String errorMsg = formatResponseMsg(resp);
            log.info(
                    "Failed to send email to advertiser {}, campaign {}, errorMsg {}. Duplicated email",
                    advertiserId,
                    campaign,
                    errorMsg);
            return Pair.of(resp, false);
        } else {
            log.info("Email sent to advertiser {}, campaign {}", advertiserId, campaign);
            return Pair.of(resp, true);
        }
    }

    private CommunicationsHubApiGatewayProtos.SendTemplateNotificationsRequest toSendNotificationRequest(
            final RavenClientPayload payload, final Map<String, Object> dataMap, final String campaignName) {
        final CommunicationsHubApiGatewayProtos.TemplateNotificationRequest request =
                getSendRequest(payload, dataMap, campaignName);
        return CommunicationsHubApiGatewayProtos.SendTemplateNotificationsRequest.newBuilder()
                .addRequests(request)
                .build();
    }

    private CommunicationsHubApiGatewayProtos.TemplateNotificationRequest getSendRequest(
            final RavenClientPayload payload, final Map<String, Object> dataMap, final String campaignName) {
        return CommunicationsHubApiGatewayProtos.TemplateNotificationRequest.newBuilder()
                .setCampaignName(campaignName)
                .setCountry(payload.getCountry())
                .setLanguage(payload.getLanguage())
                .addAllVarValues(getVarValues(dataMap))
                .setRecipients(getRecipients(payload))
                .setSourceKey(getSourceKey(payload, campaignName))
                .setDeduplicationKey(dataMap.get("idempotencyKey").toString())
                .setClientTk(ENC_CLIENT_TK)
                .build();
    }

    private CommunicationsHubApiGatewayProtos.Recipients getRecipients(final RavenClientPayload payload) {
        return CommunicationsHubApiGatewayProtos.Recipients.newBuilder()
                .setAdvertiserId((int) payload.getAdvertiserId())
                .build();
    }

    private String getSourceKey(final RavenClientPayload payload, final String campaign) {
        return payload.getAdvertiserId() + "_" + campaign;
    }

    private List<CommunicationsHubApiGatewayProtos.VarMapFieldEntry> getVarValues(final Map<String, Object> dataMap) {
        return dataMap.entrySet().stream()
                .map(e -> CommunicationsHubApiGatewayProtos.VarMapFieldEntry.newBuilder()
                        .setKey(e.getKey())
                        .addValue(CommunicationsHubApiGatewayProtos.VarValuesMapFieldEntry.newBuilder()
                                .setKey("JSON_STRING")
                                .setValue(objectToJsonString(e.getValue()))
                                .build())
                        .build())
                .toList();
    }

    private boolean isSuccess(final CommunicationsHubApiGatewayProtos.TemplateNotificationResponse response) {
        if (response != null) {
            final CommunicationsHubApiGatewayProtos.ChannelResult channelResponse = response.getChannelResult(0);
            final boolean unexpectedWebPushResults = !channelResponse
                    .getNotifiedWebpushDevicesCountByAccountEntriesList()
                    .isEmpty();
            final boolean hasNotRateLimit =
                    channelResponse.getRateLimitedLongAccountIdsList().isEmpty();

            return ((channelResponse.getResponseCode().equals(CommunicationsHubApiGatewayProtos.ResponseCode.SUCCESS)
                            || channelResponse
                                    .getResponseCode()
                                    .equals(CommunicationsHubApiGatewayProtos.ResponseCode.ACK))
                    && (!channelResponse.hasErrorMessage()
                            || channelResponse.getErrorMessage().isEmpty())
                    && hasNotRateLimit
                    && !unexpectedWebPushResults);
        }
        return false;
    }

    private boolean isDuplicated(final CommunicationsHubApiGatewayProtos.TemplateNotificationResponse response) {
        if (response != null) {
            final CommunicationsHubApiGatewayProtos.ChannelResult channelResponse = response.getChannelResult(0);
            return channelResponse.getResponseCode().equals(CommunicationsHubApiGatewayProtos.ResponseCode.DUPLICATE);
        }

        return false;
    }

    private String formatResponseMsg(
            final CommunicationsHubApiGatewayProtos.TemplateNotificationResponse templateNotificationResponse) {
        if (templateNotificationResponse != null) {
            final CommunicationsHubApiGatewayProtos.ChannelResult channelResponse =
                    templateNotificationResponse.getChannelResult(0);
            return String.format(
                    "%s %s %s",
                    channelResponse.getResponseCode(),
                    templateNotificationResponse
                            .getTemplateNotificationRequest()
                            .getUuid(),
                    channelResponse);
        }
        return "UNKNOWN UNKNOWN";
    }
}
