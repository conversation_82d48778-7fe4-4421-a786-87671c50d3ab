package com.indeed.job.is.live.audience.builder.enums;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MetricTagNames {

    public static final String TAG_BATCH_SIZE = "WALDO_BATCH_SIZE";
    public static final String TAG_RECORD_FINAL_STATE = "RECORD_FINAL_STATE";
    public static final String TAG_JOB_SUCCESS = "JOB_SUCCESS";
    public static final String TAG_EXTERNAL_SERVICE = "EXTERNAL_SERVICE";
    public static final String TAG_CAMPAIGN = "CAMPAIGN";
    public static final String TAG_CALL_SUCCESS = "SUCCESS";
}
