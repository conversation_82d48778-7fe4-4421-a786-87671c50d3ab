package com.indeed.job.is.live.audience.builder.config;

import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.enums.JobIsLiveFinalState;
import com.indeed.job.is.live.audience.builder.enums.MetricNames;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.indeed.job.is.live.audience.builder.enums.MetricNames.METRIC_EXTERNAL_CALL_PROCESS_TIME;
import static com.indeed.job.is.live.audience.builder.enums.MetricNames.METRIC_JOB_TIME_TO_LIVE_MS;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_BATCH_SIZE;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_CALL_SUCCESS;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_CAMPAIGN;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_EXTERNAL_SERVICE;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_JOB_SUCCESS;
import static com.indeed.job.is.live.audience.builder.enums.MetricTagNames.TAG_RECORD_FINAL_STATE;

@Component
public class MetricsEmitter {

    private final MeterRegistry meterRegistry;

    private final Clock clock;

    public MetricsEmitter(final MeterRegistry meterRegistry, final Clock clock) {
        this.meterRegistry = meterRegistry;
        this.clock = clock;
    }

    public void emitWaldoResultSize(final int results) {
        final Iterable<Tag> tags = List.of(Tag.of(TAG_BATCH_SIZE, Integer.toString(results)));

        meterRegistry.counter(MetricNames.METRIC_TASK_BATCH_SIZE, tags).increment();
    }

    public void emitWaldoRecordProcessed(final JobIsLiveFinalState state) {
        final Iterable<Tag> tags = List.of(Tag.of(TAG_RECORD_FINAL_STATE, state.name()));

        meterRegistry.counter(MetricNames.METRIC_TASK_RECORD_PROCESSED, tags).increment();
    }

    public void emitJobFinished(final long startTime) {
        final List<Tag> tags = List.of(Tag.of(TAG_JOB_SUCCESS, Boolean.toString(Boolean.TRUE)));

        meterRegistry
                .timer(MetricNames.METRIC_JOB_FINISHED, tags)
                .record(clock.millis() - startTime, TimeUnit.MILLISECONDS);
    }

    public void emitEmailSent(
            final int quantity,
            final ExternalService externalService,
            final String campaign,
            final Boolean callSuccess) {
        final List<Tag> tags = List.of(
                Tag.of(TAG_EXTERNAL_SERVICE, externalService.name()),
                Tag.of(TAG_CAMPAIGN, campaign),
                Tag.of(TAG_CALL_SUCCESS, Boolean.toString(callSuccess)));

        meterRegistry.counter(MetricNames.METRIC_EMAILS_SENT, tags).increment(quantity);
    }

    public void emitExternalCallProcessTime(
            final long startTime, final ExternalService externalService, final Boolean callSuccess) {
        final Iterable<Tag> tags = List.of(
                Tag.of(TAG_EXTERNAL_SERVICE, externalService.name()),
                Tag.of(TAG_CALL_SUCCESS, Boolean.toString(callSuccess)));

        meterRegistry
                .timer(METRIC_EXTERNAL_CALL_PROCESS_TIME, tags)
                .record(clock.millis() - startTime, TimeUnit.MILLISECONDS);
    }

    public void emitJobTimeToLive(final long jobDateCreated) {
        meterRegistry.timer(METRIC_JOB_TIME_TO_LIVE_MS).record(clock.millis() - jobDateCreated, TimeUnit.MILLISECONDS);
    }
}
