package com.indeed.job.is.live.audience.builder.util;

import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;

@Service
public class TimeUtil {
    public static final ZoneId DEFAULT_TIME_ZONE = ZoneId.of("Etc/UTC");

    public long getStartOfDayMilli(final Instant instant) {
        return instant.atZone(DEFAULT_TIME_ZONE)
                .toLocalDate()
                .atStartOfDay(DEFAULT_TIME_ZONE)
                .toInstant()
                .toEpochMilli();
    }

    public long convertToEpoch(final String dateTime) {
        return Instant.parse(dateTime).toEpochMilli();
    }
}
