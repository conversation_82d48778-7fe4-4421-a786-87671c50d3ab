package com.indeed.job.is.live.audience.builder.config;

import com.indeed.advertiserservice.client.AdvertiserService;
import com.indeed.dradis.data.util.AdvertiserServiceUtil;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AllArgsConstructor
public class AdvertiserServiceUtilConfig {
    final AdvertiserService advertiserService;

    @Bean
    public AdvertiserServiceUtil advertiserServiceUtil(AdvertiserService advertiserService) {
        return new AdvertiserServiceUtil(advertiserService);
    }
}
