package com.indeed.job.is.live.audience.builder.config;

import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.grpc.IndeedDiscovery;
import com.indeed.library.communicationshubapigateway.CommunicationsHubApiGatewayRpcClient;
import com.indeed.library.communicationshubapigateway.CommunicationsHubApiGatewayRpcClientFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommsHubConfig {

    public static final String CAMPAIGN_JOB_IS_LIVE = "jobislive_v1";
    public static final String CAMPAIGN_JOB_IS_LIVE_US_FTP = "jobislive_v1_us_ftp";
    public static final String CAMPAIGN_JOB_IS_LIVE_INTL_PTP = "jobislive_v1_intl_ptp";
    public static final String CAMPAIGN_JOB_IS_LIVE_JP_PTP = "jobislive_v1_JP_ptp";
    public static final String CAMPAIGN_JOB_IS_LIVE_JP_FTP = "jobislive_v1_JP_ftp";
    public static final String CAMPAIGN_JOB_IS_LIVE_V2_INTL_FTP = "jobislive_v2_intl_ftp";

    @Bean(destroyMethod = "shutdown")
    public CommunicationsHubApiGatewayRpcClient employerNotificationCenterApiRpcClient(
            final ApplicationContext context) {
        final CommunicationsHubApiGatewayRpcClientFactory factory = new CommunicationsHubApiGatewayRpcClientFactory();
        factory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        factory.setDiscovery(IndeedDiscovery.ENVOY_MESH);
        factory.extractApiKeySupplierFrom(context);
        return factory.get();
    }
}
