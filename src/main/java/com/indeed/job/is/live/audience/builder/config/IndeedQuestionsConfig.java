package com.indeed.job.is.live.audience.builder.config;

import com.indeed.iq.client.IndeedQuestionsClient;
import com.indeed.status.core.Dependency;
import com.indeed.status.core.DependencyType;
import com.indeed.status.core.PingMethod;
import com.indeed.status.core.SimplePingableDependency;
import com.indeed.status.core.Urgency;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@AllArgsConstructor
@Configuration(proxyBeanMethods = false)
public class IndeedQuestionsConfig {
    final JobIsLiveAudienceBuilderPropertiesConfig config;

    @Bean
    IndeedQuestionsClient indeedQuestionsClient(
            @Value("${ssak.IqServiceDaemon.client.JobIsLiveAudienceBuilder.api_key}")
                    final String iqServiceClientApiKey) {
        final IndeedQuestionsClient.Builder builder =
                IndeedQuestionsClient.newBuilder().setApiKey(iqServiceClientApiKey);

        return builder.build();
    }

    @Bean
    Dependency indeedQuestionsDependency(final IndeedQuestionsClient client) {
        return SimplePingableDependency.newBuilder()
                .setId("indeed-questions-service")
                .setDescription("This dependency is used to retrieve Screener Question answers for Dradis jobs. "
                        + "Failure of this dependency will result in the inability to search for jobseekers based on "
                        + "on Dradis jobs, which is in the critical path for some APIs.")
                .setDocumentationUrl("https://wiki.indeed.com/display/SQ/Indeed+Questions")
                .setType(DependencyType.StandardDependencyTypes.HTTP_SERVICE)
                .setPingMethod((PingMethod) client::checkHealthy)
                .setUrgency(Urgency.STRONG)
                .build();
    }
}
