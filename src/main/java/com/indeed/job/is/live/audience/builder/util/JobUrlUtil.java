package com.indeed.job.is.live.audience.builder.util;

import com.indeed.job.is.live.audience.builder.config.JobIsLiveAudienceBuilderPropertiesConfig;
import com.indeed.webapp.common.URLBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.indeed.job.is.live.audience.builder.util.EmployerJobUtil.atsJobIdToLegacyJobId;

@AllArgsConstructor
@Slf4j
@Service
public class JobUrlUtil {

    private static final String SPONSOR_EDIT_URL = "/sponsor/edit";
    private static final String VIEW_JOB_URL = "/jobs/view";

    private JobIsLiveAudienceBuilderPropertiesConfig config;

    public String getJobIsLiveUsFtpSponsorCtaUrl(final int advertiserId, final int atsJobId) {
        final String publishedId = atsJobIdToLegacyJobId(atsJobId, advertiserId);

        return new URLBuilder(config.getEmployerBaseUrl() + SPONSOR_EDIT_URL)
                .set("publishedId", publishedId)
                .set("sid", "us_tmp_uc_always-on_smbx_ch_email")
                .toString();
    }

    public String getJobIsLiveUsFtpViewUrl(final int advertiserId, final int atsJobId) {
        final String publishedId = atsJobIdToLegacyJobId(atsJobId, advertiserId);

        return new URLBuilder(config.getEmployerBaseUrl() + VIEW_JOB_URL)
                .set("id", publishedId)
                .set("sid", "us_tmp_uc_always-on_smbx_ch_email")
                .toString();
    }
}
