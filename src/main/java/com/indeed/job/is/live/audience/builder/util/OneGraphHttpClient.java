package com.indeed.job.is.live.audience.builder.util;

import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

public class OneGraphHttpClient {
    private final OkHttpClient httpClient;

    public OneGraphHttpClient(final Dispatcher dispatcher) {
        httpClient = new OkHttpClient.Builder()
                .dispatcher(dispatcher)
                .retryOnConnectionFailure(true)
                .build();
    }

    public OkHttpClient getHttpClient() {
        return httpClient;
    }

    public void dispose() {
        httpClient.dispatcher().cancelAll();
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
}
