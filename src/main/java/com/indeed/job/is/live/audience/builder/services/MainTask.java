package com.indeed.job.is.live.audience.builder.services;

import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientException;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.PropertyName;
import com.indeed.advertiserservice.rpc.AdvertiserServiceProtos.ShowHostedJobs;
import com.indeed.dradis.common.util.ViewJobUrlUtil;
import com.indeed.dradis.data.util.AdvertiserServiceUtil;
import com.indeed.dradis.data.util.AdvertiserServiceUtil.AdvertiserPropertyNotFoundException;
import com.indeed.format.dates.FormatDateOrTime;
import com.indeed.job.is.live.audience.builder.config.MetricsEmitter;
import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.enums.JobIsLiveFinalState;
import com.indeed.job.is.live.audience.builder.groups.JobIsLiveProctorGroups;
import com.indeed.job.is.live.audience.builder.models.JobActivityInfo;
import com.indeed.job.is.live.audience.builder.models.RavenClientPayload;
import com.indeed.job.is.live.audience.builder.models.TaskArguments;
import com.indeed.job.is.live.audience.builder.util.CommsHubService;
import com.indeed.job.is.live.audience.builder.util.CompanySizeFilterService;
import com.indeed.job.is.live.audience.builder.util.CompanySizeSegmentService;
import com.indeed.job.is.live.audience.builder.util.DocServiceUtil;
import com.indeed.job.is.live.audience.builder.util.JobUrlUtil;
import com.indeed.job.is.live.audience.builder.util.ProctorUtil;
import com.indeed.logging.client.LogEntry;
import com.indeed.logging.client.LogEntryFactory;
import com.indeed.util.core.Pair;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.io.IOException;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.time.Clock;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;

import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_INTL_PTP;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_JP_FTP;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_JP_PTP;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_US_FTP;
import static com.indeed.job.is.live.audience.builder.config.CommsHubConfig.CAMPAIGN_JOB_IS_LIVE_V2_INTL_FTP;
import static com.indeed.job.is.live.audience.builder.util.RavenIdempotencyKey.getIdempotencyKeyWithJobId;

@AllArgsConstructor
@Slf4j
@Service
public class MainTask {
    static final String JOB_IS_LIVE_EMAIL = "job_is_live_email";

    private static final String JIL_EMAIL_SOURCE = "job-is-live-audience-builder";

    private static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("US/Central");

    // We will only send 'create ad confirmation' emails if advertiser is
    // in one of the following showHostedJob states.
    private static final Set<ShowHostedJobs> ACTIVE_SHOW_HOSTED_JOBS_STATES =
            EnumSet.of(ShowHostedJobs.ENABLED, ShowHostedJobs.VERIFIED, ShowHostedJobs.VERIFIED_SPONONLY);

    private final LogEntryFactory logEntryFactory;
    private final ProctorUtil proctorUtil;
    private final AdvertiserServiceUtil advertiserServiceUtil;
    private final JobUrlUtil jobUrlUtil;
    private final DocServiceUtil docServiceUtil;
    private final MetricsEmitter metricsEmitter;
    private final Clock clock;
    private final WaldoService waldoService;

    private CommsHubService commsHubService;

    private CompanySizeSegmentService companySizeSegmentService;

    private CompanySizeFilterService companySizeFilterService;

    private final String SPONSORED_JOB = "SPONSORED";

    public void run(final TaskArguments args) throws URISyntaxException, IOException {
        final long jobProcessClock = clock.millis();
        final Map<DradisJobId, JobActivityInfo> publishActivitiesForTheseJobs =
                new HashMap<>(10000); // largish capacity, no significance otherwise
        final Timestamp now = new Timestamp(System.currentTimeMillis());

        waldoService.loadJobsFromWaldo(args, publishActivitiesForTheseJobs);

        final int jobSize = publishActivitiesForTheseJobs.size();
        metricsEmitter.emitWaldoResultSize(jobSize);

        log.info("found {} jobs that should get a DRADIS_JOBISLIVE activity", jobSize);
        docServiceUtil.setJobInfoForJobisLiveEmail(publishActivitiesForTheseJobs);
        log.info("found {} jobs have metadata in Doc services", jobSize);

        final int numHqmEmailPublished = 0;

        final Date thirtyDaysLater = new DateTime().plusDays(30).toDate();
        final List<JobActivityInfo> waldoFilterList = new ArrayList<>();
        final List<JobActivityInfo> filteredForSendJobActivityInfos = new ArrayList<>();

        for (final JobActivityInfo info : publishActivitiesForTheseJobs.values()) {
            final ShowHostedJobs showHostedJobs = getShowHostedJobs(info.getAdvertiserID());
            boolean sendEvent = true;
            final boolean jobIsSponsored;

            jobIsSponsored = info.getCampaignType().equals(SPONSORED_JOB);

            sendEvent = true;
            sendEvent &= ACTIVE_SHOW_HOSTED_JOBS_STATES.contains(showHostedJobs);
            if (ShowHostedJobs.VERIFIED_SPONONLY == showHostedJobs) {
                sendEvent &= jobIsSponsored;
            }

            // Do not send this for the first job because the welcome email is basically the same.
            sendEvent &= info.getAtsJobID() > 1;

            // send the JOBISLIVE email if this advertiser is in the proctor group.  we'll need to include
            // the advertiser language here, since that will obviously be used as the proctor criteria here
            final Locale locale;
            final long advertiserStartTime = clock.millis();
            try {
                // We use the slave for querying advertiser locale. It might happen that the advertiser is not
                // replicated to the slave
                // yet. We simply continue and let the tool handle these advertisers in the next run

                locale = advertiserServiceUtil.getPreferredLocaleWillThrow(info.getAdvertiserID());
                metricsEmitter.emitExternalCallProcessTime(
                        advertiserStartTime, ExternalService.ADVERTISER_SERVICE, true);
            } catch (final AdvertiserServiceRpcClientException | AdvertiserPropertyNotFoundException e) {
                log.info(
                        "Advertiser={} not found. Might be due to the slave replication lag. Skipping this advertiser in this run."
                                + " Not sending job is live email for advertiser_id={} ats_job_id={} agg_job_id={} jobIsSponsored={} waldoViz=%s SHJ={}",
                        info.getAdvertiserID(),
                        info.getAdvertiserID(),
                        info.getAtsJobID(),
                        info.getAggJobID(),
                        jobIsSponsored,
                        info.getWaldoViz(),
                        showHostedJobs);
                metricsEmitter.emitExternalCallProcessTime(
                        advertiserStartTime, ExternalService.ADVERTISER_SERVICE, false);
                continue;
            }

            sendEvent &= proctorUtil
                    .getProctorGroups(info.getAdvertiserID(), locale.getLanguage(), null, null)
                    .isDrat_jobliveemailActive(); // the "locale" here is a two-letter language only, no country
            // only send job is live email if the job is created within 7 days

            sendEvent &= Days.daysBetween(new DateTime(info.getDateCreated()), new DateTime(now))
                            .getDays()
                    <= args.getJobIsLiveLookbackDays();

            final JobIsLiveProctorGroups proctorGroups =
                    proctorUtil.getProctorGroups(info.getAdvertiserID(), locale.getLanguage(), info.getCountry(), null);
            info.setGroups(proctorGroups);

            final boolean raven_jobislive_intl_in_holdoutJobislive_v1 =
                    proctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v1();
            final boolean raven_jobislive_intl_in_holdoutJobislive_v1_JP =
                    proctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v1_JP();
            final boolean raven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp =
                    proctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp();
            final boolean raven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp_ptp =
                    proctorGroups.isRaven_jobislive_intl_in_holdoutJobislive_v2_intl_ptp_ftp();

            final boolean isActiveLocale = raven_jobislive_intl_in_holdoutJobislive_v1
                    || raven_jobislive_intl_in_holdoutJobislive_v1_JP
                    || raven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp
                    || raven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp_ptp;

            // only send job is live email if in valid locale and country
            log.info(
                    "Locale data for advertiser_id={} ats_job_id={} agg_job_id={}: locale language={} country={} sendEvent={} isActiveLocale={}",
                    info.getAdvertiserID(),
                    info.getAtsJobID(),
                    info.getAggJobID(),
                    locale.getLanguage(),
                    info.getCountry(),
                    sendEvent,
                    isActiveLocale);

            sendEvent &= isActiveLocale;
            // jp excludes bulk uploaded jobs
            sendEvent &= !raven_jobislive_intl_in_holdoutJobislive_v1_JP || !info.isBulkUploaded();

            if (sendEvent) {
                RavenClientPayload payload = new RavenClientPayload();
                payload.setAdvertiserId(info.getAdvertiserID());
                final String viewJobUrl =
                        ViewJobUrlUtil.getViewJobUrl(info.getAggJobID(), info.getTitle(), info.getCountry());

                log.info("Raven email data for job: {}", info);

                final ImmutableMap.Builder<String, Object> templateParams = ImmutableMap.<String, Object>builder()
                        .put("jobTitle", info.getTitle())
                        .put("jobLocation", info.getLocation())
                        .put(
                                "jobExpireDate",
                                FormatDateOrTime.formatDateOrTime(
                                        thirtyDaysLater, locale, DEFAULT_TIMEZONE, "short_date"))
                        .put("jobCompanyName", info.getCompany())
                        .put("idempotencyKey", getIdempotencyKeyWithJobId(info.getAdvertiserID(), info.getAtsJobID()));

                if (raven_jobislive_intl_in_holdoutJobislive_v1) {
                    if (jobIsSponsored) {
                        info.setRavenCampaign(CAMPAIGN_JOB_IS_LIVE);
                    } else if (locale.getLanguage().equals("en")
                            && info.getCountry().equals("US")) {
                        final String sponsorJobUrl =
                                jobUrlUtil.getJobIsLiveUsFtpSponsorCtaUrl(info.getAdvertiserID(), info.getAtsJobID());

                        info.setRavenCampaign(CAMPAIGN_JOB_IS_LIVE_US_FTP);
                        templateParams.put("jobSponsorURL", sponsorJobUrl);
                    }
                } else if (raven_jobislive_intl_in_holdoutJobislive_v1_JP) {
                    // JP
                    info.setRavenCampaign(jobIsSponsored ? CAMPAIGN_JOB_IS_LIVE_JP_PTP : CAMPAIGN_JOB_IS_LIVE_JP_FTP);
                } else if (raven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp_ptp) {
                    if (jobIsSponsored) {
                        if (locale.getLanguage().equals("en")
                                && (info.getCountry().equals("CA")
                                        || info.getCountry().equals("GB"))) {
                            info.setRavenCampaign(CAMPAIGN_JOB_IS_LIVE);
                        } else {
                            info.setRavenCampaign(CAMPAIGN_JOB_IS_LIVE_INTL_PTP);
                        }
                    } else {
                        info.setRavenCampaign(CAMPAIGN_JOB_IS_LIVE_V2_INTL_FTP);
                    }
                } else if (raven_jobislive_intl_in_holdoutJobislive_v2_intl_ftp && !jobIsSponsored) {
                    info.setRavenCampaign(CAMPAIGN_JOB_IS_LIVE_V2_INTL_FTP);
                }

                if (info.getRavenCampaign() == CAMPAIGN_JOB_IS_LIVE_US_FTP) {
                    final String viewUsFtp =
                            jobUrlUtil.getJobIsLiveUsFtpViewUrl(info.getAdvertiserID(), info.getAtsJobID());
                    templateParams.put("jobPostingURL", viewUsFtp);
                } else {
                    templateParams.put("jobPostingURL", viewJobUrl);
                }

                payload.setCountry(info.getCountry());
                payload.setLanguage(locale.getLanguage());

                payload.setDataMap(templateParams.build());
                info.setRavenPayload(payload);

                info.setGroups(proctorUtil.getProctorGroups(
                        info.getAdvertiserID(), locale.getLanguage(), info.getCountry(), info.getRavenCampaign()));

                // add job to waldo filter
                if (args.isFilterWaldoEmailsAlreadySent()) {
                    waldoFilterList.add(info);
                }

                // add to list of infos available for email sends
                filteredForSendJobActivityInfos.add(info);
                // emit jobTimeToLiveMillis metric
                if (args.isNewWaldoQuery()) {
                    metricsEmitter.emitJobTimeToLive(info.getDateCreated());
                }

                log.debug(
                        "Sending job is live email for advertiser_id={} ats_job_id={} agg_job_id={} jobIsSponsored={} jobIsBulkUploaded={} waldoViz=%s SHJ={}",
                        info.getAdvertiserID(),
                        info.getAtsJobID(),
                        info.getAggJobID(),
                        jobIsSponsored,
                        info.isBulkUploaded(),
                        info.getWaldoViz(),
                        showHostedJobs);
            } else {
                logJobIsLiveEmail(info, false, JobIsLiveFinalState.FILTERED, null, JIL_EMAIL_SOURCE, null);
                log.info(
                        "Not sending job is live email for advertiser_id={} ats_job_id={} agg_job_id={} jobIsSponsored={} jobIsBulkUploaded={} waldoViz=%s SHJ={}",
                        info.getAdvertiserID(),
                        info.getAtsJobID(),
                        info.getAggJobID(),
                        jobIsSponsored,
                        info.isBulkUploaded(),
                        info.getWaldoViz(),
                        showHostedJobs);
            }
        }

        waldoService.addIdsToFilter(waldoFilterList);

        // job is live campaign, handles en_US (PTP), en_GB, en_CA and HQM
        final List<JobActivityInfo> jobIsLiveCampaignInfos = filteredForSendJobActivityInfos.stream()
                .filter(info -> CAMPAIGN_JOB_IS_LIVE.equals(info.getRavenCampaign()))
                .collect(Collectors.toList());
        batchSend(CAMPAIGN_JOB_IS_LIVE, jobIsLiveCampaignInfos);

        // job is live campaign, handles en_US FTP
        final List<JobActivityInfo> jobIsLiveCampaignUsFtpInfos = filteredForSendJobActivityInfos.stream()
                .filter(info -> CAMPAIGN_JOB_IS_LIVE_US_FTP.equals(info.getRavenCampaign()))
                .collect(Collectors.toList());
        batchSend(CAMPAIGN_JOB_IS_LIVE_US_FTP, jobIsLiveCampaignUsFtpInfos);

        // intl ptp campaign
        final List<JobActivityInfo> jobIsLiveCampaignIntlPtpInfos = filteredForSendJobActivityInfos.stream()
                .filter(info -> CAMPAIGN_JOB_IS_LIVE_INTL_PTP.equals(info.getRavenCampaign()))
                .collect(Collectors.toList());
        batchSend(CAMPAIGN_JOB_IS_LIVE_INTL_PTP, jobIsLiveCampaignIntlPtpInfos);

        // JP ptp campaign
        final List<JobActivityInfo> jobIsLiveCampaignJpPtpInfos = filteredForSendJobActivityInfos.stream()
                .filter(info -> CAMPAIGN_JOB_IS_LIVE_JP_PTP.equals(info.getRavenCampaign()))
                .filter(info -> !companySizeFilterService.isExtraLargerEmployer(
                                companySizeSegmentService.getCompanySizeSegment(info.getAdvertiserID()))
                        && !companySizeFilterService.isLargerEmployer(
                                companySizeSegmentService.getCompanySizeSegment(info.getAdvertiserID())))
                .collect(Collectors.toList());
        batchSend(CAMPAIGN_JOB_IS_LIVE_JP_PTP, jobIsLiveCampaignJpPtpInfos);

        // JP ftp campaign
        final List<JobActivityInfo> jobIsLiveCampaignJpFtpInfos = filteredForSendJobActivityInfos.stream()
                .filter(info -> CAMPAIGN_JOB_IS_LIVE_JP_FTP.equals(info.getRavenCampaign()))
                .collect(Collectors.toList());
        batchSend(CAMPAIGN_JOB_IS_LIVE_JP_FTP, jobIsLiveCampaignJpFtpInfos);

        // INTL V2 campaign
        final List<JobActivityInfo> jobIsLiveCampaignIntV2Infos = filteredForSendJobActivityInfos.stream()
                .filter(info -> CAMPAIGN_JOB_IS_LIVE_V2_INTL_FTP.equals(info.getRavenCampaign()))
                .collect(Collectors.toList());
        batchSend(CAMPAIGN_JOB_IS_LIVE_V2_INTL_FTP, jobIsLiveCampaignIntV2Infos);

        metricsEmitter.emitJobFinished(jobProcessClock);

        log.info("(total) processed " + numHqmEmailPublished + " jobislive hqm emails");
    }

    private void batchSend(final String campaign, final List<JobActivityInfo> infos) {

        log.info("(total) emails to send {} to campaign {} using comms hub", infos.size(), campaign);

        final List<Pair<JobActivityInfo, String>> responseList = batchSendCommsHubEmails(campaign, infos);

        responseList.forEach(info -> logJobIsLiveEmail(
                info.getFirst(),
                true,
                JobIsLiveFinalState.SENT_TO_COMMS_HUB,
                campaign,
                JIL_EMAIL_SOURCE,
                info.getSecond()));
    }

    private List<Pair<JobActivityInfo, String>> batchSendCommsHubEmails(
            final String commsHubCampaign, final List<JobActivityInfo> infos) {
        final int commsHubBatchSize = 1000;
        final List<Pair<JobActivityInfo, String>> responseList = new ArrayList<>();
        for (final List<JobActivityInfo> partition : Iterables.partition(infos, commsHubBatchSize)) {
            if (partition.size() > 0) {
                final long startTime = clock.millis();
                final List<Pair<JobActivityInfo, String>> batchResponse =
                        commsHubService.sendEmails(partition, commsHubCampaign);
                responseList.addAll(batchResponse);

                metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.COMMS_HUB, true);
            }
        }
        return responseList;
    }

    private void logJobIsLiveEmail(
            final JobActivityInfo info,
            final boolean success,
            final JobIsLiveFinalState state,
            final String campaign,
            final String emailSource,
            final String serviceResponse) {
        final LogEntry logEntry = logEntryFactory.createLogEntry(JOB_IS_LIVE_EMAIL);
        final String idempotencyKey = info != null && info.getRavenPayload() != null
                ? info.getRavenPayload().getDataMap().get("idempotencyKey").toString()
                : null;
        logEntry.setProperty("advertiserId", info.getAdvertiserID());
        logEntry.setProperty("atsJobId", info.getAtsJobID());
        logEntry.setProperty("aggJobId", info.getAggJobID());
        logEntry.setProperty("campaign", campaign);
        logEntry.setProperty("success", success);
        logEntry.setProperty("grp", info.getGroups() != null ? info.getGroups().toLoggingString() : null);
        logEntry.setProperty("finalState", state.name());
        logEntry.setProperty("email_source", emailSource);
        logEntry.setProperty("dateCreate", info.getDateCreated());
        logEntry.setProperty("newWaldoQuery", info.isNewWaldoQuery());
        logEntry.setProperty("idempotencyKey", idempotencyKey);
        logEntry.setProperty("serviceResponse", serviceResponse);
        logEntry.commit();

        metricsEmitter.emitWaldoRecordProcessed(state);
    }

    @Nullable
    private ShowHostedJobs getShowHostedJobs(final int advertiserId) {
        final long startTime = clock.millis();
        final Map<PropertyName, String> advertiserProperties = advertiserServiceUtil.getAdvertiserProperties(
                advertiserId, ImmutableList.of(PropertyName.SHOW_HOSTED_JOBS));
        if (advertiserProperties != null
                && advertiserProperties.containsKey(PropertyName.SHOW_HOSTED_JOBS)
                && !Strings.isNullOrEmpty(advertiserProperties.get(PropertyName.SHOW_HOSTED_JOBS))) {
            metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.ADVERTISER_SERVICE, true);
            return ShowHostedJobs.valueOf(
                    advertiserProperties.get(PropertyName.SHOW_HOSTED_JOBS).toUpperCase());
        }

        metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.ADVERTISER_SERVICE, false);
        return null;
    }

    public static class DradisJobId {
        private final int advertiserId;
        private final int atsJobId;

        public DradisJobId(final int advertiserId, final int atsJobId) {
            this.advertiserId = advertiserId;
            this.atsJobId = atsJobId;
        }

        @Override
        public boolean equals(final Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }

            final DradisJobId that = (DradisJobId) o;

            if (advertiserId != that.advertiserId) {
                return false;
            }
            if (atsJobId != that.atsJobId) {
                return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            int result = advertiserId;
            result = 31 * result + atsJobId;
            return result;
        }
    }
}
