package com.indeed.job.is.live.audience.builder.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class RavenClientPayload {
    private long advertiserId;
    private String template;
    private String country;
    private String language;
    private Map<String, Integer> groups;
    private Map<String, Object> dataMap;
    private String adCentralEventMessage;
    private List<String> recipientEmails;
    private String ccEmail;
}
