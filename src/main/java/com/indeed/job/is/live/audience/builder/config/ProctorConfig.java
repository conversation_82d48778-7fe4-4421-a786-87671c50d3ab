package com.indeed.job.is.live.audience.builder.config;

import com.indeed.common.util.NamedThreadFactory;
import com.indeed.job.is.live.audience.builder.groups.JobIsLiveProctorGroupsManager;
import com.indeed.proctor.common.AbstractProctorLoader;
import com.indeed.proctor.internal.IndeedJsonProctorLoaderFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

@Configuration
public class ProctorConfig {

    private static final int PROCTOR_LOADER_INTERVAL_SECONDS = 300;

    @Bean
    public AbstractProctorLoader proctorSupplier() {
        final IndeedJsonProctorLoaderFactoryBean proctorLoaderFactory = new IndeedJsonProctorLoaderFactoryBean();
        final Resource proctorSpec = new ClassPathResource("proctor/proctor-specification.json");
        proctorLoaderFactory.setSpecificationResource(proctorSpec);
        return proctorLoaderFactory.getLoader();
    }

    @Bean
    public JobIsLiveProctorGroupsManager groupsManager(final AbstractProctorLoader proctorSupplier) {
        return new JobIsLiveProctorGroupsManager(proctorSupplier);
    }

    @Bean
    ScheduledExecutorService scheduledExecutorService(final AbstractProctorLoader proctorSupplier) {
        final ThreadFactory threadFactory = new NamedThreadFactory(
                "JobIsLiveAudienceBuilderProctorLoaderTasks", ScheduledThreadPoolExecutor.class.getName());
        final ScheduledThreadPoolExecutor scheduledThreadPoolExecutor =
                new ScheduledThreadPoolExecutor(4, threadFactory);
        scheduledThreadPoolExecutor.scheduleAtFixedRate(
                proctorSupplier, 0, PROCTOR_LOADER_INTERVAL_SECONDS, TimeUnit.SECONDS);
        return scheduledThreadPoolExecutor;
    }
}
