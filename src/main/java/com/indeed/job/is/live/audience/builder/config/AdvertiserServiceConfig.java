package com.indeed.job.is.live.audience.builder.config;

import com.indeed.advertiserservice.client.AdvertiserServiceRpcClient;
import com.indeed.advertiserservice.client.AdvertiserServiceRpcClientFactory;
import com.indeed.common.base.IndeedSystemProperty;
import com.indeed.grpc.IndeedDiscovery;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Nonnull;

@Configuration
public class AdvertiserServiceConfig {
    @Bean(destroyMethod = "shutdown")
    public AdvertiserServiceRpcClientFactory advertiserServiceRpcClientFactory(
            @Nonnull final ApplicationContext context) {
        final AdvertiserServiceRpcClientFactory clientFactory =
                new AdvertiserServiceRpcClientFactory(AdvertiserServiceRpcClientFactory.Pool.READ_ONLY);
        clientFactory.setDiscovery(IndeedDiscovery.ENVOY_MESH);
        clientFactory.setClientApp(IndeedSystemProperty.APPLICATION.value());
        clientFactory.extractApiKeySupplierFrom(context);
        return clientFactory;
    }

    @Bean
    public AdvertiserServiceRpcClient advertiserServiceRpcClient(
            final AdvertiserServiceRpcClientFactory clientFactory) {
        return clientFactory.get();
    }
}
