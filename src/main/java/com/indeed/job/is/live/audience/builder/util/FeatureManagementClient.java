package com.indeed.job.is.live.audience.builder.util;

import com.indeed.library.featuremanagementcommon.model.EntityType;
import com.indeed.library.featuremanagementcommon.model.ProfileValuesResult;
import com.indeed.library.featuremanagementcommon.service.FeatureManagementService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class FeatureManagementClient {

    private final FeatureManagementService featureManagementService;

    public Optional<String> getSegmentSize(final int advertiserId) throws IOException {

        final Response<ProfileValuesResult> response = featureManagementService
                .getProfileValues(EntityType.ADVERTISER.toString(), advertiserId)
                .execute();

        if (!response.isSuccessful()) {
            log.error(
                    "FeatureManagementService returns status code: {} for advertiserId: {}.",
                    response.code(),
                    advertiserId);
            return Optional.empty();
        }

        if (response.body() != null
                && response.body().getTraits() != null
                && response.body().getTraits().get("advertiser") != null) {
            final Map<String, String> advertiserDataMap =
                    response.body().getTraits().get("advertiser").entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey, e -> e.getValue().toString()));
            if (advertiserDataMap.containsKey("segmentSize")) {
                final String segmentSize = advertiserDataMap.get("segmentSize");
                log.info(
                        "FeatureManagementService returns segmentSize {} for advertiserId: {}",
                        segmentSize,
                        advertiserId);
                return Optional.of(advertiserDataMap.get("segmentSize"));
            }
        }

        return Optional.empty();
    }
}
