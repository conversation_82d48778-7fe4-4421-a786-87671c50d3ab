package com.indeed.job.is.live.audience.builder.config;

import com.indeed.common.daemons.healthcheck.DependencyTypes;
import com.indeed.library.featuremanagementcommon.config.FeatureManagementConfig;
import com.indeed.library.featuremanagementcommon.service.FeatureManagementService;
import com.indeed.status.core.PingMethod;
import com.indeed.status.core.SimplePingableDependency;
import com.indeed.status.core.Urgency;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(FeatureManagementConfig.class)
public class FeatureManagementServiceConfig {

    public SimplePingableDependency featureManagementServiceDependency(
            final FeatureManagementService featureManagementService,
            @Value("${fmp.read.timeout:250}") final long fmpTimeout) {
        return SimplePingableDependency.newBuilder()
                .setId("feature-management-service")
                .setDescription(
                        "The feature management service is used to resolve traits and segments in proctor rules.")
                .setTimeout(fmpTimeout)
                .setUrgency(Urgency.STRONG)
                .setDocumentationUrl("https://wiki.indeed.com/display/SMBPlatform/Feature+Management+Platform")
                .setType(DependencyTypes.HTTP_SERVICE)
                .setPingMethod(
                        (PingMethod) () -> featureManagementService.probe().execute())
                .build();
    }
}
