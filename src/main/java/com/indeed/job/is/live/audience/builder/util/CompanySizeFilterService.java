package com.indeed.job.is.live.audience.builder.util;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class CompanySizeFilterService {
    public static final String COMPANY_SIZE_SEGMENT_SMALL = "S";
    public static final String COMPANY_SIZE_SEGMENT_MEDIUM = "M";
    public static final String COMPANY_SIZE_SEGMENT_LARGER = "L";
    public static final String COMPANY_SIZE_SEGMENT_EXTRA_LARGER = "XL";

    public boolean isSmallEmployer(final String companySize) {
        return COMPANY_SIZE_SEGMENT_SMALL.equals(companySize);
    }

    public boolean isMediumEmployer(final String companySize) {
        return COMPANY_SIZE_SEGMENT_MEDIUM.equals(companySize);
    }

    public boolean isLargerEmployer(final String companySize) {
        return COMPANY_SIZE_SEGMENT_LARGER.equals(companySize);
    }

    public boolean isExtraLargerEmployer(final String companySize) {
        return COMPANY_SIZE_SEGMENT_EXTRA_LARGER.equals(companySize);
    }
}
