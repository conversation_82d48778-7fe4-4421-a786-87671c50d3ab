package com.indeed.job.is.live.audience.builder.services;

import com.indeed.job.is.live.audience.builder.models.TaskArguments;
import freemarker.template.utility.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class ArgParser {
    private final String JIL_LOOKBACK_DAYS_PARAM = "jobIsLiveLookbackDays";
    private final String TEST_HQM_ADV_IDS = "testHqmAdvertiserIds";
    private final String HQM_QA_TEST_RUN_PARAM = "hqmQaTestRun";
    private final String ADH_TEST_RUN_PARAM = "adhTestRun";
    private final String FILTER_WALDO_EMAILS_ALREADY_SENT = "filterWaldoEmailsAlreadySent";
    private final String NEW_WALDO_QUERY = "newWaldoQuery";

    public TaskArguments parse(final ApplicationArguments args) {
        final TaskArguments taskArguments = TaskArguments.builder()
                .jobIsLiveLookbackDays(parseIntegerWithDefault(args, JIL_LOOKBACK_DAYS_PARAM, 7))
                .testHqmAdvertiserIds(
                        args.containsOption(TEST_HQM_ADV_IDS)
                                ? Arrays.stream(StringUtil.split(
                                                args.getOptionValues("testHqmAdvertiserIds")
                                                        .get(0),
                                                ','))
                                        .map(Integer::parseInt)
                                        .collect(Collectors.toSet())
                                : new HashSet<>())
                .hqmQaTestRun(parseBooleanWithDefault(args, HQM_QA_TEST_RUN_PARAM, false))
                .adhTestRun(parseBooleanWithDefault(args, ADH_TEST_RUN_PARAM, false))
                .filterWaldoEmailsAlreadySent(parseBooleanWithDefault(args, FILTER_WALDO_EMAILS_ALREADY_SENT, true))
                .newWaldoQuery(parseBooleanWithDefault(args, NEW_WALDO_QUERY, false))
                .build();
        log.info("Arguments: {}", taskArguments);
        return taskArguments;
    }

    private int parseIntegerWithDefault(
            final ApplicationArguments args, final String paramName, final int defaultValue) {
        if (!args.containsOption(paramName)) {
            return defaultValue;
        }
        final String paramValueString = args.getOptionValues(paramName).get(0);
        return Integer.parseInt(paramValueString);
    }

    private boolean parseBooleanWithDefault(
            final ApplicationArguments args, final String paramName, final boolean defaultValue) {
        if (!args.containsOption(paramName)) {
            return defaultValue;
        }
        return "1".equals(args.getOptionValues(paramName).get(0));
    }
}
