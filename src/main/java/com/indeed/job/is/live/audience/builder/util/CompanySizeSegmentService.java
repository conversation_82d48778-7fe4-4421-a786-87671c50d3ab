package com.indeed.job.is.live.audience.builder.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Optional;

@Service
@Slf4j
public class CompanySizeSegmentService {
    private final FeatureManagementClient featureManagementClient;

    public CompanySizeSegmentService(final FeatureManagementClient featureManagementClient) {
        this.featureManagementClient = featureManagementClient;
    }

    private String getSegmentSizeFromFeatureManagement(final int advertiserId) throws IOException {
        final Optional<String> optionalSegmentSize = featureManagementClient.getSegmentSize(advertiserId);
        return optionalSegmentSize.orElse("S");
    }

    public String getCompanySizeSegment(final int advertiserId) {
        try {
            return getSegmentSizeFromFeatureManagement(advertiserId);
        } catch (final Exception e) {
            log.error("Exception when calling getDradisJobIdsInFilter() function", e);
            return "S";
        }
    }
}
