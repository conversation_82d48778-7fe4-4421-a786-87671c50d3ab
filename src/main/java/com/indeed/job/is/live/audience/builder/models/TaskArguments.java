package com.indeed.job.is.live.audience.builder.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;

import java.util.Set;

@Value
@AllArgsConstructor
@Builder
public class TaskArguments {
    private final int jobIsLiveLookbackDays;
    private final Set<Integer> testHqmAdvertiserIds;
    private final boolean hqmQaTestRun;
    private final boolean adhTestRun;
    private final boolean filterWaldoEmailsAlreadySent;
    private final boolean newWaldoQuery;
}
