package com.indeed.job.is.live.audience.builder.util;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.indeed.docservice.DocServiceException;
import com.indeed.docservice.DocServiceProtos;
import com.indeed.docservice.client.BoxcarDocServiceRpcClient;
import com.indeed.job.is.live.audience.builder.models.JobActivityInfo;
import com.indeed.job.is.live.audience.builder.services.MainTask.DradisJobId;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class DocServiceUtil {
    private static final int BATCH_SIZE = 50;
    private BoxcarDocServiceRpcClient boxcarDocServiceRpcClient;

    @VisibleForTesting
    public void setupTestConfigs(final BoxcarDocServiceRpcClient boxcarDocServiceRpcClient) {
        this.boxcarDocServiceRpcClient = boxcarDocServiceRpcClient;
    }

    public ImmutableListMultimap<DradisJobId, JobActivityInfo> setJobInfoForJobisLiveEmail(
            final Map<DradisJobId, JobActivityInfo> publishActivitiesForTheseJobs) {
        final int numJobs = publishActivitiesForTheseJobs.keySet().size();
        log.info("Beginning to get job data for {} jobs in doc service", numJobs);
        final ImmutableListMultimap.Builder<DradisJobId, JobActivityInfo> builder = ImmutableListMultimap.builder();
        int count = 0;
        for (final List<DradisJobId> jobBatch :
                Lists.partition(new ArrayList<>(publishActivitiesForTheseJobs.keySet()), BATCH_SIZE)) {
            final Map<Long, DradisJobId> jobMap = jobBatch.stream()
                    .collect(Collectors.toMap(
                            key -> publishActivitiesForTheseJobs.get(key).getAggJobID(), a -> a));

            final DocServiceProtos.DocRequest request = DocServiceProtos.DocRequest.newBuilder()
                    .addAllOrganicResultsJobIds(jobMap.keySet())
                    .build();

            final DocServiceProtos.DocResponse response;
            try {
                response = boxcarDocServiceRpcClient.service(request, null);
            } catch (final DocServiceException e) {
                log.error("Failed to retrieve job info from DocServiceClient", e);
                continue;
            }
            final List<DocServiceProtos.Job> docJobList = response.getOrganicResultsList().stream()
                    .map(DocServiceProtos.JobResult::getJob)
                    .collect(Collectors.toList());

            for (final DocServiceProtos.Job docJob : docJobList) {
                final long aggJobId = docJob.getId();
                final String jobTitle = docJob.getTitle();
                final String company = docJob.getCompany();
                final String zipCode = docJob.getZip();
                final String location = Strings.isNullOrEmpty(zipCode)
                        ? docJob.getCity() + "," + docJob.getState()
                        : docJob.getCity() + "," + docJob.getState() + "," + zipCode;
                final DradisJobId jobId = jobMap.get(aggJobId);

                if (jobId == null) {
                    log.error("Could not find job id {} from doc service in list of jobs", aggJobId);
                } else {
                    final JobActivityInfo jobInfo = publishActivitiesForTheseJobs.get(jobId);
                    jobInfo.setLocation(location);
                    jobInfo.setCompany(company);
                    jobInfo.setTitle(jobTitle);
                    builder.put(jobId, jobInfo);

                    log.info(
                            "Set Job Info from Doc Service advertiserId {}, atsJobId {}, aggJobId {}, location {}, company {}, title {}",
                            jobInfo.getAdvertiserID(),
                            jobInfo.getAtsJobID(),
                            jobInfo.getAggJobID(),
                            location,
                            company,
                            jobTitle);
                }
            }
            count += jobBatch.size();
            final String percent = String.format("%.2f", ((float) count / publishActivitiesForTheseJobs.size()) * 100);
            log.info(
                    "Retrieved job data for {} / {} jobs ({}). Not found: {}.",
                    count,
                    publishActivitiesForTheseJobs.size(),
                    percent,
                    jobBatch.size() - docJobList.size());
        }
        final ImmutableListMultimap<DradisJobId, JobActivityInfo> jobsFromDocService = builder.build();
        final int numJobsMissing = numJobs - jobsFromDocService.values().size();
        final float numJobsMissingPercent = (float) numJobsMissing / (float) numJobs;
        log.error(
                "Number of jobs missing from DocService ({} / {}) = {}",
                numJobsMissing,
                numJobs,
                numJobsMissingPercent);
        return jobsFromDocService;
    }
}
