package com.indeed.job.is.live.audience.builder.util;

import com.google.common.collect.ImmutableMap;
import com.indeed.job.is.live.audience.builder.groups.JobIsLiveProctorGroups;
import com.indeed.job.is.live.audience.builder.groups.JobIsLiveProctorGroupsManager;
import com.indeed.proctor.common.Identifiers;
import com.indeed.proctor.common.ProctorResult;
import com.indeed.proctor.common.model.TestType;
import com.indeed.proctor.consumer.ProctorConsumerUtils;
import com.indeed.proctor.internal.TestTypes;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.LocaleUtils;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Service
@AllArgsConstructor
@Slf4j
@ComponentScan("com.indeed.job.is.live.builder.config")
public class ProctorUtil {
    private final JobIsLiveProctorGroupsManager proctorGroupsManager;

    /**
     * Determine the proctor groups for test types RANDOM.
     *
     * @return The groups for test type RANDOM
     *
     * https://wiki.indeed.com/display/eng/Proctor+Test+Types#ProctorTestTypes-RANDOM
     * https://indeed.sourcegraph.com/code.corp.indeed.com/opensource/proctor/-/blob/proctor-common/src/main/java/com/indeed/proctor/common/Identifiers.java?L29:34
     */
    @Nonnull
    public JobIsLiveProctorGroups getProctorGroups() {
        return getProctorGroups(null, null, null, null, null, null, null, null);
    }

    /**
     * Determine the proctor groups for the current advertiser.
     *
     * @param advertiserId The current advertiser we are building proctor groups for.
     *
     * @return The groups for the given advertiser
     */
    @Nonnull
    public JobIsLiveProctorGroups getProctorGroups(final int advertiserId) {
        return getProctorGroups(advertiserId, null, null, null);
    }

    /**
     * Determine the proctor groups for the current advertiser.
     *
     * @param advertiserId The current advertiser we are building proctor groups for.
     *
     * @return The groups for the given advertiser
     */
    @Nonnull
    public JobIsLiveProctorGroups getProctorGroups(
            final int advertiserId,
            @Nullable final String hl,
            @Nullable final String co,
            @Nullable final String campaign) {
        return getProctorGroups(advertiserId, hl, co, null, null, null, null, campaign);
    }

    /**
     * Determine the proctor groups for the current advertiser. A request parameter is taken to allow for force groups.
     *
     * @param advertiserId The current advertiser we are building proctor groups for.
     * @param hl language of the advertiser.
     * @param co country of the advertiser.
     * @param request The request that the prforceGroups parameter is attached to.
     *
     * @return The groups for the given advertiser, including the force groups if the request is provided.
     */
    @Nonnull
    public JobIsLiveProctorGroups getProctorGroups(
            @Nullable final Integer advertiserId,
            @Nullable final String hl,
            @Nullable final String co,
            @Nullable final HttpServletRequest request,
            @Nullable final Map advertiserAttributes,
            @Nullable final String jobCountry,
            @Nullable final String jobLanguage,
            @Nullable final String campaign) {
        final ImmutableMap.Builder<TestType, String> identifierValues = ImmutableMap.builder();
        identifierValues.put(TestTypes.AUTHENTICATED_USER, String.valueOf(advertiserId));
        identifierValues.put(TestTypes.ADVERTISER, String.valueOf(advertiserId));
        final Identifiers identifiers = new Identifiers(identifierValues.build(), true);
        ProctorResult proctorResult = ProctorResult.EMPTY;

        Map attributes = advertiserAttributes == null ? ImmutableMap.of() : advertiserAttributes;

        try {
            final String ln = hl != null ? (LocaleUtils.toLocale(hl).getLanguage()) : null;
            if (request != null) {
                // Check for force groups
                final Map<String, Integer> forcedGroups = ProctorConsumerUtils.parseForcedGroups(request);
                proctorResult = proctorGroupsManager.determineBuckets(
                        identifiers,
                        forcedGroups,
                        advertiserId,
                        hl,
                        ln,
                        co,
                        co,
                        attributes,
                        jobCountry,
                        jobLanguage,
                        campaign);
            } else {
                proctorResult = proctorGroupsManager.determineBuckets(
                        identifiers, advertiserId, hl, ln, co, co, attributes, jobCountry, jobLanguage, campaign);
            }

        } catch (Exception e) {
            log.error("Error determining Proctor test group buckets, using fallback values", e);
        }
        return new JobIsLiveProctorGroups(proctorResult);
    }
}
