package com.indeed.job.is.live.audience.builder.services;

import com.google.common.collect.Iterables;
import com.indeed.job.is.live.audience.builder.config.MetricsEmitter;
import com.indeed.job.is.live.audience.builder.config.WaldoQueryConfig;
import com.indeed.job.is.live.audience.builder.enums.ExternalService;
import com.indeed.job.is.live.audience.builder.models.JobActivityInfo;
import com.indeed.job.is.live.audience.builder.models.TaskArguments;
import com.indeed.job.is.live.audience.builder.util.EmployerJobUtil;
import com.indeed.job.is.live.audience.builder.util.ProctorUtil;
import com.indeed.job.is.live.audience.builder.util.TimeUtil;
import com.indeed.one.graph.client.EmployerJobQuery;
import com.indeed.squall.waldocommon.WaldoClient;
import com.indeed.squall.waldocommon.apibuilder.AggregationQuery;
import com.indeed.squall.waldocommon.model.api.AggregationQueryResponse;
import com.indeed.util.core.Pair;
import com.indeed.waldojobindex.rule.PublisherFilterPairedItem;
import com.indeed.waldojobindex.search.refineby.RefineByType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.time.Clock;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@AllArgsConstructor
@Slf4j
@Service
public class WaldoService {

    private final String FREE_JOB = "FTP";
    private final String SPONSORED_JOB = "SPONSORED";
    private final String WALDO_FILTER = "JobIsLiveEmail";

    private final Clock clock;
    private final WaldoQueryConfig waldoQueryConfig;
    private final WaldoClient waldoClient;
    private final ProctorUtil proctorUtil;
    private final MetricsEmitter metricsEmitter;
    private final TimeUtil timeUtil;
    private final EmployerJobUtil employerJobUtil;

    public void loadJobsFromWaldo(
            final TaskArguments args, final Map<MainTask.DradisJobId, JobActivityInfo> publishActivitiesForTheseJobs)
            throws URISyntaxException, IOException {

        final AggregationQuery query = getAggregationQuery(args);

        final long startTime = clock.millis();
        final AggregationQueryResponse response =
                waldoClient.doAggregationQuery(query.setReturnNames(true).setReturnJobIds(true));
        final Map<? extends Number, String> uuidMap = response.getIdNamesForType(RefineByType.EMPLOYER_JOB_ID);
        metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.WALDO, true);

        final List<PublisherFilterPairedItem> publisherFilterPairedItems = new ArrayList<>();
        log.info("Retrieved {} jobs from Waldo", response.resultBuckets.size());

        response.resultBuckets.forEach(bucket -> {
            final String[] ids = bucket.bucketKey.toString().split("-");
            final int advertiserId = Integer.parseInt(ids[0]);
            final int atsJobId = Integer.parseInt(ids[1]);

            publisherFilterPairedItems.add(new PublisherFilterPairedItem(advertiserId, atsJobId));

            final MainTask.DradisJobId dradisJobId = new MainTask.DradisJobId(advertiserId, atsJobId);

            bucket.subBuckets.forEach((key, subBucket) -> {
                final int advertisementId = Integer.parseInt(key.toString());
                final Optional<AggregationQueryResponse.RefineByBucket> maySubBucket =
                        subBucket.subBuckets.values().stream().findFirst();

                String country = "";
                long aggJobId = 0;
                String uuid = "";
                boolean isBulkUploaded = false;
                // waldo query age:0 will return all jobs created within the last 24 hours from midnight
                long dateCreatedMilli = timeUtil.getStartOfDayMilli(Instant.now());
                if (maySubBucket.isPresent()) {
                    if (args.isNewWaldoQuery()) {
                        uuid = uuidMap.get(
                                Long.valueOf((maySubBucket.get().bucketKey.toString())));
                    } else {
                        country = Integer.toString((int) maySubBucket.get().bucketKey, 36)
                                .toUpperCase();
                    }
                    if ((maySubBucket.get().jobIds != null) && (maySubBucket.get().jobIds.length > 0)) {
                        // like the original method, we dedup multiple locations and only save *one* of the locations,
                        // arbitrarily
                        aggJobId = maySubBucket.get().jobIds[0];
                    }
                }

                if (aggJobId > 0) {
                    if (args.isNewWaldoQuery()) {
                        final Optional<EmployerJobQuery.OnHostedJobPost> mayHasJob =
                                employerJobUtil.getEmployerJobPost(uuid, advertiserId);
                        if (mayHasJob.isPresent()) {
                            final EmployerJobQuery.OnHostedJobPost hostedJobPost = mayHasJob.get();
                            final String formattedCreatDate = hostedJobPost.dateCreated.toString();
                            dateCreatedMilli = timeUtil.convertToEpoch(formattedCreatDate);
                            country = hostedJobPost.country.toString();
                            isBulkUploaded = hostedJobPost.attributes.stream()
                                    .anyMatch(attr -> "bulkuploaded".equals(attr.key) && "true".equals(attr.value));

                            log.info(
                                    "hosted job advertiser = {}, atsJobId = {}, formatted create date = {}, date created = {},  country = {}, isBulkUploaded = {}",
                                    advertiserId,
                                    atsJobId,
                                    formattedCreatDate,
                                    dateCreatedMilli,
                                    country,
                                    isBulkUploaded);
                        }
                    }

                    log.info(
                            "advertiser id = {}, atsjobId = {}, advertisementId = {}, job created = {}, country = {}, isBulkUploaded = {}, aggJobId = {} from Waldo",
                            advertiserId,
                            atsJobId,
                            advertisementId,
                            dateCreatedMilli,
                            country,
                            isBulkUploaded,
                            aggJobId);

                    final JobActivityInfo jobActivityInfoToPublish = JobActivityInfo.builder()
                            .advertiserID(advertiserId)
                            .atsJobID(atsJobId)
                            .campaignType((advertisementId == 0) ? FREE_JOB : SPONSORED_JOB)
                            .dateCreated(dateCreatedMilli)
                            .country(country)
                            .aggJobID(aggJobId)
                            .isBulkUploaded(isBulkUploaded)
                            .company("")
                            .title("")
                            .location("")
                            .icrClaimStatus("")
                            .newWaldoQuery(args.isNewWaldoQuery())
                            .build();

                    if ((args.isNewWaldoQuery()
                                    && proctorUtil
                                            .getProctorGroups(advertiserId)
                                            .isJob_is_live_new_waldo_query_testNewquery())
                            || (!args.isNewWaldoQuery()
                                    && proctorUtil
                                            .getProctorGroups(advertiserId)
                                            .isJob_is_live_new_waldo_query_testOldquery())) {
                        publishActivitiesForTheseJobs.put(dradisJobId, jobActivityInfoToPublish);
                    } else {
                        log.info(
                                "advertiser id = {}, atsjobId = {}, newWaldoQuery = {}, not in the active group",
                                advertiserId,
                                atsJobId,
                                args.isNewWaldoQuery());
                        ;
                    }
                }
            });
        });

        // remove the job which already got the email
        final Set<MainTask.DradisJobId> dradisJobIdsInFilter = getDradisJobIdsInFilter(publisherFilterPairedItems);
        publishActivitiesForTheseJobs.keySet().removeAll(dradisJobIdsInFilter);
    }

    private @NotNull AggregationQuery getAggregationQuery(TaskArguments args) {
        final AggregationQuery query;

        if (args.isNewWaldoQuery()) {
            log.info("Loading jobs from Waldo using new query");
            final String queryString = String.format(
                    "job_age_in_minutes:<=%s has_advertiserid:1 dradis:yes (visibility:(jobalert OR organic) OR (visibility:sponsoredonly AND activead:1)) -autoclaim_claimed_job:1 -is_claimed:1",
                    waldoQueryConfig.getJobAgeInMinutes());
            log.info("The New Waldo query:" + queryString);
            query = new AggregationQuery(
                    Arrays.asList(
                            RefineByType.JOB_POST_ID, RefineByType.ADVERTISEMENT_ID, RefineByType.EMPLOYER_JOB_ID),
                    queryString);
        } else {
            query = new AggregationQuery(
                    Arrays.asList(RefineByType.JOB_POST_ID, RefineByType.ADVERTISEMENT_ID, RefineByType.COUNTRY),
                    "pubage:0 has_advertiserid:1 (visibility:(jobalert OR organic) OR (visibility:sponsoredonly AND activead:1)) -autoclaim_claimed_job:1 -is_claimed:1");
        }
        return query;
    }

    private Set<MainTask.DradisJobId> getDradisJobIdsInFilter(
            final List<PublisherFilterPairedItem> publisherFilterPairedItems) {
        final Set<MainTask.DradisJobId> dradisJobIds = new HashSet<>();
        try {
            for (final List<PublisherFilterPairedItem> partition :
                    Iterables.partition(publisherFilterPairedItems, waldoQueryConfig.getBatchSize())) {

                final List<PublisherFilterPairedItem> itemsInPairFilter =
                        waldoClient.checkItemsInPairFilter(WALDO_FILTER, partition);
                log.info("{} ids already in {} filter", itemsInPairFilter.size(), WALDO_FILTER);
                dradisJobIds.addAll(itemsInPairFilter.stream()
                        .map(p -> new MainTask.DradisJobId((int) p.item1, (int) p.item2))
                        .collect(Collectors.toSet()));
            }

        } catch (final Exception e) {
            log.error("Exception when calling getDradisJobIdsInFilter() function", e);
        }
        return dradisJobIds;
    }

    public void addIdsToFilter(final List<JobActivityInfo> jobs) {
        if (jobs.isEmpty()) {
            return;
        }

        boolean waldoCallSuccess = false;
        final long startTime = clock.millis();

        try {
            final List<Pair<Integer, Integer>> ids = jobs.stream()
                    .map(p -> new Pair<>(p.getAdvertiserID(), p.getAtsJobID()))
                    .collect(Collectors.toList());
            log.info("Passing {} ids to {} filter", ids.size(), WALDO_FILTER);

            waldoClient.addIdsToFilter(WALDO_FILTER, ids);
            waldoCallSuccess = true;
        } catch (final Exception e) {
            log.error("Exception when calling addIdsToFilter() function", e);
        } finally {
            metricsEmitter.emitExternalCallProcessTime(startTime, ExternalService.WALDO, waldoCallSuccess);
        }
    }
}
