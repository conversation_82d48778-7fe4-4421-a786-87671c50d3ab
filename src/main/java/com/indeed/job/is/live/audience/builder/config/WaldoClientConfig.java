package com.indeed.job.is.live.audience.builder.config;

import com.indeed.squall.waldocommon.WaldoClient;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class WaldoClientConfig {
    final JobIsLiveAudienceBuilderPropertiesConfig config;
    private static final int WALDO_RETRY_TIME = 5;

    @Bean
    public WaldoClient waldoClient() {
        return WaldoClient.newBuilder()
                .useApiKey(config.getWaldoUrl(), config.getWaldoApiKey())
                .setAutomaticRetryCount(WALDO_RETRY_TIME)
                .setRetryWaitMs(WaldoClient.DEFAULT_RETRY_WAIT_MS)
                .build();
    }
}
