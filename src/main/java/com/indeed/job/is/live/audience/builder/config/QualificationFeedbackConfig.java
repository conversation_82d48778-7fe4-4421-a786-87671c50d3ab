package com.indeed.job.is.live.audience.builder.config;

import com.indeed.common.daemons.healthcheck.DependencyTypes;
import com.indeed.grpc.IndeedDiscovery;
import com.indeed.qualification.feedback.service.client.QualificationFeedbackService;
import com.indeed.qualification.feedback.service.client.QualificationFeedbackServiceRpcClientFactory;
import com.indeed.status.core.PingMethod;
import com.indeed.status.core.SimplePingableDependency;
import com.indeed.status.core.Urgency;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class QualificationFeedbackConfig {

    @Bean
    public QualificationFeedbackService qualificationFeedbackService(final ApplicationContext context) {
        final QualificationFeedbackServiceRpcClientFactory factory = new QualificationFeedbackServiceRpcClientFactory();
        factory.extractApiKeySupplierFrom(context);
        factory.setDiscovery(IndeedDiscovery.ENVOY_MESH);
        return factory.get();
    }

    @Bean
    public SimplePingableDependency qualificationFeedbackServiceDependency(
            final QualificationFeedbackService qualificationFeedbackService) {
        return SimplePingableDependency.newBuilder()
                .setId("qualificationFeedbackService")
                .setDescription("QualificationFeedbackService RPC for retrieving job feedbacks.")
                .setDocumentationUrl("https://wiki.indeed.com/display/SMBHiring/Qualification+Feedback+Service")
                .setUrgency(Urgency.WEAK)
                .setType(DependencyTypes.BOXCAR_SERVICE)
                .setPingMethod((PingMethod) qualificationFeedbackService::probe)
                .build();
    }
}
