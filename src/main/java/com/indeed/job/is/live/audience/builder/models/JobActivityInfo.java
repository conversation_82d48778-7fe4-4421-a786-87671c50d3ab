package com.indeed.job.is.live.audience.builder.models;

import com.indeed.job.is.live.audience.builder.groups.JobIsLiveProctorGroups;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class JobActivityInfo {
    private int advertiserID;
    private int atsJobID;
    private long aggJobID;
    private String company;
    private String country;
    private String title;
    private String location;
    private String campaignType;
    private String icrClaimStatus;
    private int waldoViz;
    private String ravenCampaign;
    private RavenClientPayload ravenPayload;
    private long dateCreated;
    private JobIsLiveProctorGroups groups;
    private boolean newWaldoQuery;
    private boolean isBulkUploaded;
}
