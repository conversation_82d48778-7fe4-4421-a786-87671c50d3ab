package com.indeed.job.is.live.audience.builder.enums;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MetricNames {

    private static final String METRICS_PREFIX = "job.is.live.";
    public static final String METRIC_TASK_BATCH_SIZE = METRICS_PREFIX + "task.batch.size";
    public static final String METRIC_TASK_RECORD_PROCESSED = METRICS_PREFIX + "task.record.processed";
    public static final String METRIC_TASK_RECORD_PROCESSED_TIME = METRICS_PREFIX + "task.record.processed.time";
    public static final String METRIC_JOB_FINISHED = METRICS_PREFIX + "job.finished";
    public static final String METRIC_EMAILS_SENT = METRICS_PREFIX + "job.email.sent";

    public static final String METRIC_EXTERNAL_CALL_PROCESS_TIME = METRICS_PREFIX + "call.processtime";
    public static final String METRIC_JOB_TIME_TO_LIVE_MS = METRICS_PREFIX + "job.to.live";
}
