package com.indeed.job.is.live.audience.builder.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class WaldoQueryConfig {
    @Value("${waldo.query.batchSize:100}")
    private int batchSize;

    @Value("${waldo.query.jobAgeInMinutes:60}")
    private int JobAgeInMinutes;
}
